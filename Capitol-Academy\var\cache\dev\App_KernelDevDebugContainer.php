<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerFWomIF9\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerFWomIF9/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerFWomIF9.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerFWomIF9\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerFWomIF9\App_KernelDevDebugContainer([
    'container.build_hash' => 'FWomIF9',
    'container.build_id' => 'a0dafb6e',
    'container.build_time' => 1752684114,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerFWomIF9');
