<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerCxjHiNK\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerCxjHiNK/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerCxjHiNK.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerCxjHiNK\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerCxjHiNK\App_KernelDevDebugContainer([
    'container.build_hash' => 'CxjHiNK',
    'container.build_id' => 'bbdd1995',
    'container.build_time' => 1752668124,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerCxjHiNK');
