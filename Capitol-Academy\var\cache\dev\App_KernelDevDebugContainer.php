<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerQcpp4p2\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerQcpp4p2/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerQcpp4p2.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerQcpp4p2\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerQcpp4p2\App_KernelDevDebugContainer([
    'container.build_hash' => 'Qcpp4p2',
    'container.build_id' => '26f8e9a9',
    'container.build_time' => 1752679700,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerQcpp4p2');
