<?php

namespace ContainerCxjHiNK;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_Qyz7DBHService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.Qyz7DBH' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.Qyz7DBH'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'adminRepository' => ['privates', 'App\\Repository\\AdminRepository', 'getAdminRepositoryService', true],
        ], [
            'adminRepository' => 'App\\Repository\\AdminRepository',
        ]);
    }
}
