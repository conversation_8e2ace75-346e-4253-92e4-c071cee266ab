<?php

namespace ContainerFWomIF9;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_LKvMsLFService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.LKvMsLF' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.LKvMsLF'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', 'App\\Repository\\CourseRepository', 'getCourseRepositoryService', true],
            'partnerRepository' => ['privates', 'App\\Repository\\PartnerRepository', 'getPartnerRepositoryService', false],
            'videoRepository' => ['privates', 'App\\Repository\\VideoRepository', 'getVideoRepositoryService', true],
        ], [
            'courseRepository' => 'App\\Repository\\CourseRepository',
            'partnerRepository' => 'App\\Repository\\PartnerRepository',
            'videoRepository' => 'App\\Repository\\VideoRepository',
        ]);
    }
}
