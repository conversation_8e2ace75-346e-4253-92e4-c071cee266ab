<?php

namespace ContainerCxjHiNK;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAdminMarketAnalysisControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\AdminMarketAnalysisController' shared autowired service.
     *
     * @return \App\Controller\AdminMarketAnalysisController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'framework-bundle'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AbstractController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AdminMarketAnalysisController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'string'.\DIRECTORY_SEPARATOR.'Slugger'.\DIRECTORY_SEPARATOR.'SluggerInterface.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'string'.\DIRECTORY_SEPARATOR.'Slugger'.\DIRECTORY_SEPARATOR.'AsciiSlugger.php';

        $container->services['App\\Controller\\AdminMarketAnalysisController'] = $instance = new \App\Controller\AdminMarketAnalysisController(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Repository\\MarketAnalysisRepository'] ?? $container->load('getMarketAnalysisRepositoryService')), ($container->privates['App\\Service\\AdminPermissionService'] ?? $container->load('getAdminPermissionServiceService')), ($container->privates['App\\Service\\ErrorHandlingService'] ?? $container->load('getErrorHandlingServiceService')), ($container->privates['debug.validator'] ?? self::getDebug_ValidatorService($container)), ($container->privates['slugger'] ??= new \Symfony\Component\String\Slugger\AsciiSlugger('en')));

        $instance->setContainer(($container->privates['.service_locator.O2p6Lk7'] ?? $container->load('get_ServiceLocator_O2p6Lk7Service'))->withContext('App\\Controller\\AdminMarketAnalysisController', $container));

        return $instance;
    }
}
