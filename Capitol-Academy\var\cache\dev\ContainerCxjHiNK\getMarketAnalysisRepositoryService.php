<?php

namespace ContainerCxjHiNK;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getMarketAnalysisRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\MarketAnalysisRepository' shared autowired service.
     *
     * @return \App\Repository\MarketAnalysisRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'MarketAnalysisRepository.php';

        return $container->privates['App\\Repository\\MarketAnalysisRepository'] = new \App\Repository\MarketAnalysisRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
