<?php

// This file has been auto-generated by the Symfony Dependency Injection Component
// You can reference it in the "opcache.preload" php.ini setting on PHP >= 7.4 when preloading is desired

use Symfony\Component\DependencyInjection\Dumper\Preloader;

if (in_array(PHP_SAPI, ['cli', 'phpdbg', 'embed'], true)) {
    return;
}

require dirname(__DIR__, 3).''.\DIRECTORY_SEPARATOR.'vendor/autoload.php';
(require __DIR__.'/App_KernelDevDebugContainer.php')->set(\ContainerFWomIF9\App_KernelDevDebugContainer::class, null);
require __DIR__.'/ContainerFWomIF9/EntityManagerGhostEbeb667.php';
require __DIR__.'/ContainerFWomIF9/RequestPayloadValueResolverGhost3590451.php';
require __DIR__.'/ContainerFWomIF9/ProfilerProxy0a5fddb.php';
require __DIR__.'/ContainerFWomIF9/getWebProfiler_Controller_RouterService.php';
require __DIR__.'/ContainerFWomIF9/getWebProfiler_Controller_ProfilerService.php';
require __DIR__.'/ContainerFWomIF9/getWebProfiler_Controller_ExceptionPanelService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_UploadHandlerService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Storage_FileSystemService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_PropertyMappingFactoryService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Upload_InstructorProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Upload_HomepageSectionsService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Upload_AdminProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Remove_InstructorProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Remove_HomepageSectionsService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Remove_AdminProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Clean_InstructorProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Clean_HomepageSectionsService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Listener_Clean_AdminProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Form_Type_ImageService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_Form_Type_FileService.php';
require __DIR__.'/ContainerFWomIF9/getVichUploader_DownloadHandlerService.php';
require __DIR__.'/ContainerFWomIF9/getValidator_WhenService.php';
require __DIR__.'/ContainerFWomIF9/getValidator_NotCompromisedPasswordService.php';
require __DIR__.'/ContainerFWomIF9/getValidator_NoSuspiciousCharactersService.php';
require __DIR__.'/ContainerFWomIF9/getValidator_ExpressionLanguageService.php';
require __DIR__.'/ContainerFWomIF9/getValidator_ExpressionService.php';
require __DIR__.'/ContainerFWomIF9/getValidator_EmailService.php';
require __DIR__.'/ContainerFWomIF9/getTwig_Runtime_SerializerService.php';
require __DIR__.'/ContainerFWomIF9/getTwig_Runtime_SecurityCsrfService.php';
require __DIR__.'/ContainerFWomIF9/getTwig_Runtime_ImportmapService.php';
require __DIR__.'/ContainerFWomIF9/getTwig_Runtime_HttpkernelService.php';
require __DIR__.'/ContainerFWomIF9/getTwig_Mailer_MessageListenerService.php';
require __DIR__.'/ContainerFWomIF9/getTwig_Form_RendererService.php';
require __DIR__.'/ContainerFWomIF9/getTwig_Form_EngineService.php';
require __DIR__.'/ContainerFWomIF9/getTurbo_Twig_RuntimeService.php';
require __DIR__.'/ContainerFWomIF9/getTurbo_Doctrine_EventListenerService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_YmlService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_XliffService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_ResService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_QtService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_PoService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_PhpService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_MoService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_JsonService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_IniService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_DatService.php';
require __DIR__.'/ContainerFWomIF9/getTranslation_Loader_CsvService.php';
require __DIR__.'/ContainerFWomIF9/getTexter_TransportsService.php';
require __DIR__.'/ContainerFWomIF9/getStimulus_UxControllersTwigRuntimeService.php';
require __DIR__.'/ContainerFWomIF9/getStimulus_AssetMapper_LoaderJavascriptCompilerService.php';
require __DIR__.'/ContainerFWomIF9/getStimulus_AssetMapper_ControllersMapGeneratorService.php';
require __DIR__.'/ContainerFWomIF9/getSession_Handler_NativeService.php';
require __DIR__.'/ContainerFWomIF9/getSession_FactoryService.php';
require __DIR__.'/ContainerFWomIF9/getServicesResetterService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Validator_UserPasswordService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_UserPasswordHasherService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_UserCheckerLocatorService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_User_Provider_Concrete_AppUserProviderService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_User_Provider_Concrete_AppUnifiedProviderService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_User_Provider_Concrete_AppAdminProviderService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_RouteLoader_LogoutService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_PasswordHasherFactoryService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Logout_Listener_Default_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Logout_Listener_Default_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Logout_Listener_CsrfTokenClearingService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Logout_Listener_CookieClearing_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Logout_Listener_CookieClearing_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_UserProviderService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_UserChecker_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_UserChecker_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_Session_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_Session_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_RememberMe_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_PasswordMigratingService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_Main_UserProviderService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_CsrfProtectionService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_CheckRememberMeConditions_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_CheckAuthenticatorCredentialsService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Listener_Admin_UserProviderService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_HttpUtilsService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Firewall_Map_Context_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Firewall_Map_Context_DevService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Firewall_Map_Context_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Firewall_EventDispatcherLocatorService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Csrf_TokenStorageService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Csrf_TokenManagerService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_ChannelListenerService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authenticator_RememberMeHandler_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authenticator_RememberMe_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authenticator_ManagersLocatorService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authenticator_Manager_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authenticator_Manager_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authenticator_FormLogin_MainService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authenticator_FormLogin_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_AuthenticationUtilsService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_Authentication_SessionStrategyService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_AccessMapService.php';
require __DIR__.'/ContainerFWomIF9/getSecurity_AccessListenerService.php';
require __DIR__.'/ContainerFWomIF9/getSecrets_VaultService.php';
require __DIR__.'/ContainerFWomIF9/getRouting_LoaderService.php';
require __DIR__.'/ContainerFWomIF9/getPropertyInfo_SerializerExtractorService.php';
require __DIR__.'/ContainerFWomIF9/getNotifier_TransportFactory_NullService.php';
require __DIR__.'/ContainerFWomIF9/getMonolog_Logger_MessengerService.php';
require __DIR__.'/ContainerFWomIF9/getMonolog_Logger_MailerService.php';
require __DIR__.'/ContainerFWomIF9/getMonolog_Logger_DeprecationService.php';
require __DIR__.'/ContainerFWomIF9/getMonolog_Logger_AssetMapperService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_TransportFactoryService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Transport_Sync_FactoryService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Transport_FailedService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Transport_Doctrine_FactoryService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Transport_AsyncService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_RoutableMessageBusService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Retry_SendFailedMessageForRetryListenerService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Retry_MultiplierRetryStrategy_FailedService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Retry_MultiplierRetryStrategy_AsyncService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Listener_StopWorkerOnRestartSignalListenerService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Failure_SendFailedMessageToFailureTransportListenerService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Bus_Default_Middleware_TraceableService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Bus_Default_Middleware_SendMessageService.php';
require __DIR__.'/ContainerFWomIF9/getMessenger_Bus_Default_Middleware_HandleMessageService.php';
require __DIR__.'/ContainerFWomIF9/getMailer_TransportsService.php';
require __DIR__.'/ContainerFWomIF9/getMailer_TransportFactory_SmtpService.php';
require __DIR__.'/ContainerFWomIF9/getMailer_TransportFactory_SendmailService.php';
require __DIR__.'/ContainerFWomIF9/getMailer_TransportFactory_NullService.php';
require __DIR__.'/ContainerFWomIF9/getMailer_TransportFactory_NativeService.php';
require __DIR__.'/ContainerFWomIF9/getMailer_MailerService.php';
require __DIR__.'/ContainerFWomIF9/getKnpu_Oauth2_RegistryService.php';
require __DIR__.'/ContainerFWomIF9/getKnpu_Oauth2_Client_GoogleService.php';
require __DIR__.'/ContainerFWomIF9/getInstructorImageNamer_InstructorProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getFragment_Renderer_InlineService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeGuesser_ValidatorService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeGuesser_DoctrineService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_Upload_ValidatorService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_Password_PasswordHasherService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_Form_ValidatorService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_Form_TransformationFailureHandlingService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_Form_PasswordHasherService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_Form_HttpFoundationService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_Form_DataCollectorService.php';
require __DIR__.'/ContainerFWomIF9/getForm_TypeExtension_CsrfService.php';
require __DIR__.'/ContainerFWomIF9/getForm_Type_FormService.php';
require __DIR__.'/ContainerFWomIF9/getForm_Type_FileService.php';
require __DIR__.'/ContainerFWomIF9/getForm_Type_EntityService.php';
require __DIR__.'/ContainerFWomIF9/getForm_Type_ColorService.php';
require __DIR__.'/ContainerFWomIF9/getForm_Type_ChoiceService.php';
require __DIR__.'/ContainerFWomIF9/getForm_ServerParamsService.php';
require __DIR__.'/ContainerFWomIF9/getForm_RegistryService.php';
require __DIR__.'/ContainerFWomIF9/getForm_Listener_PasswordHasherService.php';
require __DIR__.'/ContainerFWomIF9/getForm_FactoryService.php';
require __DIR__.'/ContainerFWomIF9/getForm_ChoiceListFactory_CachedService.php';
require __DIR__.'/ContainerFWomIF9/getErrorHandler_ErrorRenderer_HtmlService.php';
require __DIR__.'/ContainerFWomIF9/getErrorControllerService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_UuidGeneratorService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_UlidGeneratorService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_Validator_UniqueService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_Messenger_EventSubscriber_DoctrineClearEntityManagerService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_Messenger_DoctrineSchemaListenerService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_Listeners_PdoSessionHandlerSchemaListenerService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_Listeners_LockStoreSchemaListenerService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_Listeners_DoctrineTokenProviderSchemaListenerService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_Listeners_DoctrineDbalCacheAdapterSchemaListenerService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_DefaultListeners_AttachEntityListenersService.php';
require __DIR__.'/ContainerFWomIF9/getDoctrine_Orm_DefaultEntityManager_PropertyInfoExtractorService.php';
require __DIR__.'/ContainerFWomIF9/getDebug_Security_Voter_VoteListenerService.php';
require __DIR__.'/ContainerFWomIF9/getDebug_Security_Firewall_Authenticator_MainService.php';
require __DIR__.'/ContainerFWomIF9/getDebug_Security_Firewall_Authenticator_AdminService.php';
require __DIR__.'/ContainerFWomIF9/getDebug_FileLinkFormatter_UrlFormatService.php';
require __DIR__.'/ContainerFWomIF9/getDebug_ErrorHandlerConfiguratorService.php';
require __DIR__.'/ContainerFWomIF9/getDataCollector_Request_SessionCollectorService.php';
require __DIR__.'/ContainerFWomIF9/getController_TemplateAttributeListenerService.php';
require __DIR__.'/ContainerFWomIF9/getContainer_GetenvService.php';
require __DIR__.'/ContainerFWomIF9/getContainer_GetRoutingConditionServiceService.php';
require __DIR__.'/ContainerFWomIF9/getContainer_EnvVarProcessorsLocatorService.php';
require __DIR__.'/ContainerFWomIF9/getContainer_EnvVarProcessorService.php';
require __DIR__.'/ContainerFWomIF9/getCache_SystemClearerService.php';
require __DIR__.'/ContainerFWomIF9/getCache_GlobalClearerService.php';
require __DIR__.'/ContainerFWomIF9/getCache_AppClearerService.php';
require __DIR__.'/ContainerFWomIF9/getAssetMapper_Importmap_GeneratorService.php';
require __DIR__.'/ContainerFWomIF9/getAssetMapper_Importmap_ConfigReaderService.php';
require __DIR__.'/ContainerFWomIF9/getAssetMapper_Compiler_JavascriptImportPathCompilerService.php';
require __DIR__.'/ContainerFWomIF9/getAssetMapper_Compiler_CssAssetUrlCompilerService.php';
require __DIR__.'/ContainerFWomIF9/getTransliteratorService.php';
require __DIR__.'/ContainerFWomIF9/getUploaderExtensionRuntimeService.php';
require __DIR__.'/ContainerFWomIF9/getUniqidNamerService.php';
require __DIR__.'/ContainerFWomIF9/getSubdirDirectoryNamerService.php';
require __DIR__.'/ContainerFWomIF9/getSmartUniqueNamer_HomepageSectionsService.php';
require __DIR__.'/ContainerFWomIF9/getSmartUniqueNamer_AdminProfilesService.php';
require __DIR__.'/ContainerFWomIF9/getSmartUniqueNamerService.php';
require __DIR__.'/ContainerFWomIF9/getPropertyNamerService.php';
require __DIR__.'/ContainerFWomIF9/getPropertyDirectoryNamerService.php';
require __DIR__.'/ContainerFWomIF9/getOrignameNamerService.php';
require __DIR__.'/ContainerFWomIF9/getHashNamerService.php';
require __DIR__.'/ContainerFWomIF9/getCurrentDateTimeDirectoryNamerService.php';
require __DIR__.'/ContainerFWomIF9/getConfigurableDirectoryNamerService.php';
require __DIR__.'/ContainerFWomIF9/getBase64NamerService.php';
require __DIR__.'/ContainerFWomIF9/getTemplateControllerService.php';
require __DIR__.'/ContainerFWomIF9/getRedirectControllerService.php';
require __DIR__.'/ContainerFWomIF9/getProfilerControllerService.php';
require __DIR__.'/ContainerFWomIF9/getValidationServiceService.php';
require __DIR__.'/ContainerFWomIF9/getIpAddressServiceService.php';
require __DIR__.'/ContainerFWomIF9/getInstructorImageNamerService.php';
require __DIR__.'/ContainerFWomIF9/getErrorHandlingServiceService.php';
require __DIR__.'/ContainerFWomIF9/getEmailUniquenessValidatorService.php';
require __DIR__.'/ContainerFWomIF9/getCartServiceService.php';
require __DIR__.'/ContainerFWomIF9/getAdminPermissionServiceService.php';
require __DIR__.'/ContainerFWomIF9/getAccessControlServiceService.php';
require __DIR__.'/ContainerFWomIF9/getVideoRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getUserVideoAccessRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getUserRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getPlanRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getPaymentRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getPasswordResetTokenRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getOrderRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getMarketAnalysisRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getInstructorRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getCourseReviewRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getCourseRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getCourseModuleRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getCountryRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getContactRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getCategoryRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getAdminRepositoryService.php';
require __DIR__.'/ContainerFWomIF9/getVideoTypeService.php';
require __DIR__.'/ContainerFWomIF9/getRegistrationFormTypeService.php';
require __DIR__.'/ContainerFWomIF9/getPromotionalBannerTypeService.php';
require __DIR__.'/ContainerFWomIF9/getPlanTypeService.php';
require __DIR__.'/ContainerFWomIF9/getPartnerTypeService.php';
require __DIR__.'/ContainerFWomIF9/getMarketAnalysisTypeService.php';
require __DIR__.'/ContainerFWomIF9/getLoginFormTypeService.php';
require __DIR__.'/ContainerFWomIF9/getInstructorTypeService.php';
require __DIR__.'/ContainerFWomIF9/getCourseTypeService.php';
require __DIR__.'/ContainerFWomIF9/getContactTypeService.php';
require __DIR__.'/ContainerFWomIF9/getCategoryTypeService.php';
require __DIR__.'/ContainerFWomIF9/getAdminFormTypeService.php';
require __DIR__.'/ContainerFWomIF9/getVideoController2Service.php';
require __DIR__.'/ContainerFWomIF9/getUserOrderControllerService.php';
require __DIR__.'/ContainerFWomIF9/getUserControllerService.php';
require __DIR__.'/ContainerFWomIF9/getSecurityControllerService.php';
require __DIR__.'/ContainerFWomIF9/getSearchControllerService.php';
require __DIR__.'/ContainerFWomIF9/getPublicCourseControllerService.php';
require __DIR__.'/ContainerFWomIF9/getPaymentControllerService.php';
require __DIR__.'/ContainerFWomIF9/getPasswordResetControllerService.php';
require __DIR__.'/ContainerFWomIF9/getMarketAnalysisControllerService.php';
require __DIR__.'/ContainerFWomIF9/getHomeControllerService.php';
require __DIR__.'/ContainerFWomIF9/getGoogleAuthControllerService.php';
require __DIR__.'/ContainerFWomIF9/getCourseControllerService.php';
require __DIR__.'/ContainerFWomIF9/getContactControllerService.php';
require __DIR__.'/ContainerFWomIF9/getCheckoutControllerService.php';
require __DIR__.'/ContainerFWomIF9/getCartControllerService.php';
require __DIR__.'/ContainerFWomIF9/getVideoControllerService.php';
require __DIR__.'/ContainerFWomIF9/getOrderControllerService.php';
require __DIR__.'/ContainerFWomIF9/getDashboardControllerService.php';
require __DIR__.'/ContainerFWomIF9/getCategoryControllerService.php';
require __DIR__.'/ContainerFWomIF9/getAdminSecurityControllerService.php';
require __DIR__.'/ContainerFWomIF9/getAdminMarketAnalysisControllerService.php';
require __DIR__.'/ContainerFWomIF9/getAdminInstructorControllerService.php';
require __DIR__.'/ContainerFWomIF9/getAdminControllerService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_Y4Zrx_Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_UVvF4gLService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_RuxHxcAService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_RavAt47Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_RSTd_NAService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_MCbvPMWService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_KleoBdAService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_JqHm011Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_Jezs8TRService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_HokdgZZService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_C7f47p7Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_C34BrisService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_Bki8G0JService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_ZdZvBz9Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_YuEbyXLService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_WZeIXfOService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_WGDSHn2Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_SoIa2O7Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_Qyz7DBHService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_QkVYtxVService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_O2p6Lk7Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_Mhqdd2rService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_LVjjCt5Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_LKvMsLFService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_L29W5HSService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_Hz5btgeService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_DcCEeyOService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_CsMkqUaService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_Bd5JDSLService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_8RUuq9sService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_8IESKP1Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_6lJWFv4Service.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_4qavBNKService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_4n4ylFvService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator_1IiHidaService.php';
require __DIR__.'/ContainerFWomIF9/get_ServiceLocator__XI94lUService.php';
require __DIR__.'/ContainerFWomIF9/get_Security_RequestMatcher_KLbKLHaService.php';
require __DIR__.'/ContainerFWomIF9/get_Security_RequestMatcher_5eRZPSiService.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_VMw0m61Service.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_TGvt0LHService.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_P4QvabmService.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_KEzMhfsService.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_XZowc_TService.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_QXXNQ9dService.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_Lml2ICsService.php';
require __DIR__.'/ContainerFWomIF9/get_Messenger_HandlerDescriptor_6kVvRT_Service.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_Security_UserValueResolverService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_Security_SecurityTokenValueResolverService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_Doctrine_Orm_EntityValueResolverService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_VariadicService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_SessionService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_ServiceService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_RequestPayloadService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_RequestAttributeService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_RequestService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_QueryParameterValueResolverService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_NotTaggedControllerService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_DefaultService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_DatetimeService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_ValueResolver_ArgumentResolver_BackedEnumResolverService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_Security_Voter_Security_Access_SimpleRoleVoterService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_Security_Voter_Security_Access_ExpressionVoterService.php';
require __DIR__.'/ContainerFWomIF9/get_Debug_Security_Voter_Security_Access_AuthenticatedVoterService.php';

$classes = [];
$classes[] = 'Symfony\Bundle\FrameworkBundle\FrameworkBundle';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\DoctrineBundle';
$classes[] = 'Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle';
$classes[] = 'Symfony\Bundle\DebugBundle\DebugBundle';
$classes[] = 'Symfony\Bundle\TwigBundle\TwigBundle';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\WebProfilerBundle';
$classes[] = 'Symfony\UX\StimulusBundle\StimulusBundle';
$classes[] = 'Symfony\UX\Turbo\TurboBundle';
$classes[] = 'Twig\Extra\TwigExtraBundle\TwigExtraBundle';
$classes[] = 'Symfony\Bundle\SecurityBundle\SecurityBundle';
$classes[] = 'Symfony\Bundle\MonologBundle\MonologBundle';
$classes[] = 'Symfony\Bundle\MakerBundle\MakerBundle';
$classes[] = 'Doctrine\Bundle\FixturesBundle\DoctrineFixturesBundle';
$classes[] = 'Vich\UploaderBundle\VichUploaderBundle';
$classes[] = 'KnpU\OAuth2ClientBundle\KnpUOAuth2ClientBundle';
$classes[] = 'Symfony\Component\HttpKernel\Profiler\Profiler';
$classes[] = 'Symfony\Component\HttpKernel\Profiler\FileProfilerStorage';
$classes[] = 'Monolog\Logger';
$classes[] = 'Symfony\Component\Console\DataCollector\CommandDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\TimeDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\MemoryDataCollector';
$classes[] = 'Symfony\Component\Validator\DataCollector\ValidatorDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\AjaxDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\ExceptionDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\LoggerDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\EventDataCollector';
$classes[] = 'Symfony\Component\Translation\DataCollector\TranslationDataCollector';
$classes[] = 'Symfony\Bundle\SecurityBundle\DataCollector\SecurityDataCollector';
$classes[] = 'Symfony\Bridge\Twig\DataCollector\TwigDataCollector';
$classes[] = 'Symfony\Component\HttpClient\DataCollector\HttpClientDataCollector';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\DataCollector\DoctrineDataCollector';
$classes[] = 'Symfony\Component\Messenger\DataCollector\MessengerDataCollector';
$classes[] = 'Symfony\Component\Mailer\DataCollector\MessageDataCollector';
$classes[] = 'Symfony\Component\Notifier\DataCollector\NotificationDataCollector';
$classes[] = 'Vich\UploaderBundle\DataCollector\MappingCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector';
$classes[] = 'Symfony\Component\HttpClient\TraceableHttpClient';
$classes[] = 'Symfony\Component\HttpClient\UriTemplateHttpClient';
$classes[] = 'Symfony\Contracts\HttpClient\HttpClientInterface';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\TraceableVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\ExpressionVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\ExpressionLanguage';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\RoleVoter';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\BackedEnumValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DateTimeValueResolver';
$classes[] = 'Symfony\Component\Clock\Clock';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\NotTaggedControllerValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\QueryParameterValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver';
$classes[] = 'Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver';
$classes[] = 'Symfony\Component\ExpressionLanguage\ExpressionLanguage';
$classes[] = 'Symfony\Component\Security\Http\Controller\SecurityTokenValueResolver';
$classes[] = 'Symfony\Component\Security\Http\Controller\UserValueResolver';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlerDescriptor';
$classes[] = 'Symfony\Component\HttpClient\Messenger\PingWebhookMessageHandler';
$classes[] = 'Symfony\Component\Notifier\Messenger\MessageHandler';
$classes[] = 'Symfony\Component\Process\Messenger\RunProcessMessageHandler';
$classes[] = 'Symfony\Component\Console\Messenger\RunCommandMessageHandler';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Console\Application';
$classes[] = 'Symfony\Component\Messenger\Handler\RedispatchMessageHandler';
$classes[] = 'Symfony\Component\Mailer\Messenger\MessageHandler';
$classes[] = 'Symfony\Component\Notifier\Transport\Transports';
$classes[] = 'Symfony\Component\Notifier\Transport';
$classes[] = 'Symfony\Component\HttpFoundation\ChainRequestMatcher';
$classes[] = 'Symfony\Component\HttpFoundation\RequestMatcher\PathRequestMatcher';
$classes[] = 'Symfony\Component\DependencyInjection\ServiceLocator';
$classes[] = 'Symfony\Component\HttpKernel\Debug\VirtualRequestStack';
$classes[] = 'App\Controller\AdminController';
$classes[] = 'App\Controller\AdminInstructorController';
$classes[] = 'App\Controller\AdminMarketAnalysisController';
$classes[] = 'App\Controller\AdminSecurityController';
$classes[] = 'App\Controller\Admin\CategoryController';
$classes[] = 'App\Controller\Admin\DashboardController';
$classes[] = 'App\Controller\Admin\OrderController';
$classes[] = 'App\Controller\Admin\VideoController';
$classes[] = 'App\Controller\CartController';
$classes[] = 'App\Controller\CheckoutController';
$classes[] = 'App\Service\PayPalService';
$classes[] = 'App\Controller\ContactController';
$classes[] = 'App\Service\UnifiedContactService';
$classes[] = 'App\Controller\CourseController';
$classes[] = 'App\Controller\GoogleAuthController';
$classes[] = 'App\Controller\HomeController';
$classes[] = 'App\Controller\MarketAnalysisController';
$classes[] = 'App\Controller\PasswordResetController';
$classes[] = 'App\Service\PasswordResetService';
$classes[] = 'App\Controller\PaymentController';
$classes[] = 'App\Service\StripeService';
$classes[] = 'App\Service\AdminNotificationService';
$classes[] = 'App\Controller\PublicCourseController';
$classes[] = 'App\Controller\SearchController';
$classes[] = 'App\Controller\SecurityController';
$classes[] = 'App\Controller\UserController';
$classes[] = 'App\Controller\UserOrderController';
$classes[] = 'App\Controller\VideoController';
$classes[] = 'App\Form\AdminFormType';
$classes[] = 'App\Form\CategoryType';
$classes[] = 'App\Form\ContactType';
$classes[] = 'App\Form\CourseType';
$classes[] = 'App\Form\InstructorType';
$classes[] = 'App\Form\LoginFormType';
$classes[] = 'App\Form\MarketAnalysisType';
$classes[] = 'App\Form\PartnerType';
$classes[] = 'App\Form\PlanType';
$classes[] = 'App\Form\PromotionalBannerType';
$classes[] = 'App\Form\RegistrationFormType';
$classes[] = 'App\Form\VideoType';
$classes[] = 'App\Repository\AdminRepository';
$classes[] = 'App\Repository\CategoryRepository';
$classes[] = 'App\Repository\ContactRepository';
$classes[] = 'App\Repository\CountryRepository';
$classes[] = 'App\Repository\CourseModuleRepository';
$classes[] = 'App\Repository\CourseRepository';
$classes[] = 'App\Repository\CourseReviewRepository';
$classes[] = 'App\Repository\InstructorRepository';
$classes[] = 'App\Repository\MarketAnalysisRepository';
$classes[] = 'App\Repository\OrderRepository';
$classes[] = 'App\Repository\PartnerRepository';
$classes[] = 'App\Repository\PasswordResetTokenRepository';
$classes[] = 'App\Repository\PaymentRepository';
$classes[] = 'App\Repository\PlanRepository';
$classes[] = 'App\Repository\PromotionalBannerRepository';
$classes[] = 'App\Repository\UserRepository';
$classes[] = 'App\Repository\UserVideoAccessRepository';
$classes[] = 'App\Repository\VideoRepository';
$classes[] = 'App\Security\UserChecker';
$classes[] = 'App\Service\AccessControlService';
$classes[] = 'App\Service\VdoCipherService';
$classes[] = 'App\Service\AdminPermissionService';
$classes[] = 'App\Service\CartService';
$classes[] = 'App\Service\EmailUniquenessValidator';
$classes[] = 'App\Service\ErrorHandlingService';
$classes[] = 'App\Service\InstructorImageNamer';
$classes[] = 'App\Service\IpAddressService';
$classes[] = 'App\Service\ValidationService';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Controller\ProfilerController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\RedirectController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\TemplateController';
$classes[] = 'Vich\UploaderBundle\Naming\Base64Namer';
$classes[] = 'Vich\UploaderBundle\Naming\ConfigurableDirectoryNamer';
$classes[] = 'Vich\UploaderBundle\Naming\CurrentDateTimeDirectoryNamer';
$classes[] = 'Vich\UploaderBundle\Naming\HashNamer';
$classes[] = 'Vich\UploaderBundle\Naming\OrignameNamer';
$classes[] = 'Vich\UploaderBundle\Naming\PropertyDirectoryNamer';
$classes[] = 'Vich\UploaderBundle\Naming\PropertyNamer';
$classes[] = 'Vich\UploaderBundle\Naming\SmartUniqueNamer';
$classes[] = 'Vich\UploaderBundle\Naming\SubdirDirectoryNamer';
$classes[] = 'Vich\UploaderBundle\Naming\UniqidNamer';
$classes[] = 'Vich\UploaderBundle\Twig\Extension\UploaderExtensionRuntime';
$classes[] = 'Vich\UploaderBundle\Templating\Helper\UploaderHelper';
$classes[] = 'Vich\UploaderBundle\Util\Transliterator';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestPayloadValueResolver';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapper';
$classes[] = 'Symfony\Component\AssetMapper\Factory\CachedMappedAssetFactory';
$classes[] = 'Symfony\Component\AssetMapper\Factory\MappedAssetFactory';
$classes[] = 'Symfony\Component\AssetMapper\Path\PublicAssetsPathResolver';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperCompiler';
$classes[] = 'Symfony\Component\AssetMapper\CompiledAssetMapperConfigReader';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\CssAssetUrlCompiler';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\JavaScriptImportPathCompiler';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\SourceMappingUrlsCompiler';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperDevServerSubscriber';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapConfigReader';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapGenerator';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\RemotePackageStorage';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperRepository';
$classes[] = 'Symfony\Component\Asset\Packages';
$classes[] = 'Symfony\Component\AssetMapper\MapperAwareAssetPackage';
$classes[] = 'Symfony\Component\Asset\PathPackage';
$classes[] = 'Symfony\Component\Asset\VersionStrategy\EmptyVersionStrategy';
$classes[] = 'Symfony\Component\Asset\Context\RequestStackContext';
$classes[] = 'Symfony\Component\Cache\Adapter\TraceableAdapter';
$classes[] = 'Symfony\Component\Cache\Adapter\AdapterInterface';
$classes[] = 'Symfony\Component\Cache\Adapter\AbstractAdapter';
$classes[] = 'Symfony\Component\Cache\Adapter\FilesystemAdapter';
$classes[] = 'Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer';
$classes[] = 'Symfony\Component\Cache\Marshaller\DefaultMarshaller';
$classes[] = 'Symfony\Component\Cache\Adapter\ArrayAdapter';
$classes[] = 'Symfony\Component\Config\Resource\SelfCheckingResourceChecker';
$classes[] = 'Symfony\Component\Config\ResourceCheckerConfigCacheFactory';
$classes[] = 'Symfony\Component\DependencyInjection\EnvVarProcessor';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\CacheAttributeListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\IsGrantedAttributeListener';
$classes[] = 'Symfony\Bridge\Twig\EventListener\TemplateAttributeListener';
$classes[] = 'Symfony\Component\Cache\DataCollector\CacheDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\DumpDataCollector';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\FormDataCollector';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\FormDataExtractor';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\RequestDataCollector';
$classes[] = 'Symfony\Bundle\FrameworkBundle\DataCollector\RouterDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DebugHandlersListener';
$classes[] = 'Symfony\Component\HttpKernel\Log\DebugLoggerConfigurator';
$classes[] = 'Symfony\Component\HttpKernel\Debug\ErrorHandlerConfigurator';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\FileLinkFormatter';
$classes[] = 'Symfony\Bridge\Monolog\Processor\DebugProcessor';
$classes[] = 'Symfony\Component\Security\Core\Authorization\TraceableAccessDecisionManager';
$classes[] = 'Symfony\Component\Security\Core\Authorization\AccessDecisionManager';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Strategy\AffirmativeStrategy';
$classes[] = 'Symfony\Component\EventDispatcher\Debug\TraceableEventDispatcher';
$classes[] = 'Symfony\Component\EventDispatcher\EventDispatcher';
$classes[] = 'Symfony\Bundle\SecurityBundle\Debug\TraceableFirewallListener';
$classes[] = 'Symfony\Component\Security\Http\Authenticator\Debug\TraceableAuthenticatorManagerListener';
$classes[] = 'Symfony\Component\Security\Http\Firewall\AuthenticatorManagerListener';
$classes[] = 'Symfony\Bundle\SecurityBundle\EventListener\VoteListener';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableSerializer';
$classes[] = 'Symfony\Component\Serializer\Serializer';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UnwrappingDenormalizer';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\Normalizer\FlattenExceptionNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ProblemNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UidNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ConstraintViolationListNormalizer';
$classes[] = 'Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter';
$classes[] = 'Symfony\Component\Serializer\Normalizer\MimeMessageNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\PropertyNormalizer';
$classes[] = 'Symfony\Component\Serializer\Mapping\ClassDiscriminatorFromClassMetadata';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeZoneNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateIntervalNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\FormErrorNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\BackedEnumNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DataUriNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\TranslatableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\JsonSerializableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ArrayDenormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ObjectNormalizer';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\XmlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\JsonEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\YamlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\CsvEncoder';
$classes[] = 'Symfony\Component\Stopwatch\Stopwatch';
$classes[] = 'Symfony\Component\Validator\Validator\TraceableValidator';
$classes[] = 'Symfony\Component\Validator\Validator\ValidatorInterface';
$classes[] = 'Symfony\Component\DependencyInjection\Config\ContainerParametersResourceChecker';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DisallowRobotsIndexingListener';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Registry';
$classes[] = 'Doctrine\DBAL\Connection';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\ConnectionFactory';
$classes[] = 'Doctrine\DBAL\Configuration';
$classes[] = 'Doctrine\DBAL\Schema\LegacySchemaManagerFactory';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager';
$classes[] = 'Doctrine\DBAL\Logging\Middleware';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Middleware\DebugMiddleware';
$classes[] = 'Doctrine\DBAL\Tools\DsnParser';
$classes[] = 'Symfony\Bridge\Doctrine\ContainerAwareEventManager';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Middleware\BacktraceDebugDataHolder';
$classes[] = 'Doctrine\ORM\Mapping\Driver\AttributeDriver';
$classes[] = 'Doctrine\ORM\Configuration';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Mapping\MappingDriver';
$classes[] = 'Doctrine\Persistence\Mapping\Driver\MappingDriverChain';
$classes[] = 'Doctrine\ORM\Mapping\UnderscoreNamingStrategy';
$classes[] = 'Doctrine\ORM\Mapping\DefaultQuoteStrategy';
$classes[] = 'Doctrine\ORM\Mapping\DefaultTypedFieldMapper';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Mapping\ContainerEntityListenerResolver';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Repository\ContainerRepositoryFactory';
$classes[] = 'Doctrine\ORM\Proxy\Autoloader';
$classes[] = 'Doctrine\ORM\EntityManager';
$classes[] = 'Symfony\Bridge\Doctrine\PropertyInfo\DoctrineExtractor';
$classes[] = 'Doctrine\ORM\Tools\AttachEntityListenersListener';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\ManagerConfigurator';
$classes[] = 'Doctrine\ORM\Mapping\Driver\SimplifiedXmlDriver';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\DoctrineDbalCacheAdapterSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\RememberMeTokenProviderDoctrineSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\LockStoreSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\PdoSessionHandlerSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\MessengerTransportDoctrineSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\Messenger\DoctrineClearEntityManagerWorkerSubscriber';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntityValidator';
$classes[] = 'Symfony\Bridge\Doctrine\IdGenerator\UlidGenerator';
$classes[] = 'Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator';
$classes[] = 'Doctrine\Bundle\MigrationsBundle\EventListener\SchemaFilterListener';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ErrorController';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\SerializerErrorRenderer';
$classes[] = 'Symfony\Bridge\Twig\ErrorRenderer\TwigErrorRenderer';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer';
$classes[] = 'Symfony\Component\HttpKernel\Debug\TraceableEventDispatcher';
$classes[] = 'Monolog\Handler\NullHandler';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ErrorListener';
$classes[] = 'Symfony\Component\Form\ChoiceList\Factory\CachingFactoryDecorator';
$classes[] = 'Symfony\Component\Form\ChoiceList\Factory\PropertyAccessDecorator';
$classes[] = 'Symfony\Component\Form\ChoiceList\Factory\DefaultChoiceListFactory';
$classes[] = 'Symfony\Component\Form\FormFactory';
$classes[] = 'Symfony\Component\Form\Extension\PasswordHasher\EventListener\PasswordHasherListener';
$classes[] = 'Symfony\Component\Form\FormRegistry';
$classes[] = 'Symfony\Component\Form\Extension\DependencyInjection\DependencyInjectionExtension';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\Proxy\ResolvedTypeFactoryDataCollectorProxy';
$classes[] = 'Symfony\Component\Form\ResolvedFormTypeFactory';
$classes[] = 'Symfony\Component\Form\Util\ServerParams';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\ChoiceType';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\ColorType';
$classes[] = 'Symfony\Bridge\Doctrine\Form\Type\EntityType';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\FileType';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\FormType';
$classes[] = 'Symfony\Component\Form\Extension\Csrf\Type\FormTypeCsrfExtension';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\Type\DataCollectorTypeExtension';
$classes[] = 'Symfony\Component\Form\Extension\HttpFoundation\Type\FormTypeHttpFoundationExtension';
$classes[] = 'Symfony\Component\Form\Extension\HttpFoundation\HttpFoundationRequestHandler';
$classes[] = 'Symfony\Component\Form\Extension\PasswordHasher\Type\FormTypePasswordHasherExtension';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\TransformationFailureExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\FormTypeValidatorExtension';
$classes[] = 'Symfony\Component\Form\Extension\PasswordHasher\Type\PasswordTypePasswordHasherExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\RepeatedTypeValidatorExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\SubmitTypeValidatorExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\UploadValidatorExtension';
$classes[] = 'Symfony\Bridge\Doctrine\Form\DoctrineOrmTypeGuesser';
$classes[] = 'Symfony\Component\Form\Extension\Validator\ValidatorTypeGuesser';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\InlineFragmentRenderer';
$classes[] = 'Symfony\Component\HttpClient\HttpClient';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\HttpKernelRunner';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\ResponseRunner';
$classes[] = 'Symfony\Component\Runtime\SymfonyRuntime';
$classes[] = 'Symfony\Component\HttpKernel\HttpKernel';
$classes[] = 'Symfony\Component\HttpKernel\Controller\TraceableControllerResolver';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\ControllerResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\TraceableArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadataFactory';
$classes[] = 'App\Kernel';
$classes[] = 'KnpU\OAuth2ClientBundle\Client\Provider\GoogleClient';
$classes[] = 'League\OAuth2\Client\Provider\Google';
$classes[] = 'KnpU\OAuth2ClientBundle\DependencyInjection\ProviderFactory';
$classes[] = 'KnpU\OAuth2ClientBundle\Client\ClientRegistry';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleAwareListener';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\EnvelopeListener';
$classes[] = 'Symfony\Component\Mailer\Mailer';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageLoggerListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessengerTransportListener';
$classes[] = 'Symfony\Component\Mailer\Transport\NativeTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\NullTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\SendmailTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Transports';
$classes[] = 'Symfony\Component\Mailer\Transport';
$classes[] = 'Symfony\Component\Messenger\Middleware\AddBusNameStampMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\HandleMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlersLocator';
$classes[] = 'Symfony\Component\Messenger\Middleware\SendMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Transport\Sender\SendersLocator';
$classes[] = 'Symfony\Component\Messenger\Middleware\TraceableMiddleware';
$classes[] = 'Symfony\Component\Messenger\TraceableMessageBus';
$classes[] = 'Symfony\Component\Messenger\MessageBus';
$classes[] = 'Symfony\Component\Messenger\EventListener\AddErrorDetailsStampListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\SendFailedMessageToFailureTransportListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnRestartSignalListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnCustomStopExceptionListener';
$classes[] = 'Symfony\Component\Messenger\Middleware\DispatchAfterCurrentBusMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\FailedMessageProcessingMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\RejectRedeliveredMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Retry\MultiplierRetryStrategy';
$classes[] = 'Symfony\Component\Messenger\EventListener\SendFailedMessageForRetryListener';
$classes[] = 'Symfony\Component\Messenger\RoutableMessageBus';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportInterface';
$classes[] = 'Symfony\Component\Messenger\Bridge\Doctrine\Transport\DoctrineTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\InMemory\InMemoryTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\PhpSerializer';
$classes[] = 'Symfony\Component\Messenger\Transport\Sync\SyncTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportFactory';
$classes[] = 'Symfony\Component\Mime\MimeTypes';
$classes[] = 'Symfony\Bridge\Monolog\Handler\ConsoleHandler';
$classes[] = 'Monolog\Handler\StreamHandler';
$classes[] = 'Monolog\Processor\PsrLogMessageProcessor';
$classes[] = 'Symfony\Component\Notifier\EventListener\NotificationLoggerListener';
$classes[] = 'Symfony\Component\Notifier\Transport\NullTransportFactory';
$classes[] = 'Symfony\Component\DependencyInjection\ParameterBag\ContainerBag';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ProfilerListener';
$classes[] = 'Symfony\Component\PropertyAccess\PropertyAccessor';
$classes[] = 'Symfony\Component\PropertyInfo\PropertyInfoExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\PhpStanExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\SerializerExtractor';
$classes[] = 'Symfony\Component\HttpFoundation\RequestStack';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ResponseListener';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\Router';
$classes[] = 'Symfony\Component\Routing\Matcher\ExpressionLanguageProvider';
$classes[] = 'Symfony\Component\Routing\RequestContext';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\RouterListener';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\DelegatingLoader';
$classes[] = 'Symfony\Component\Config\Loader\LoaderResolver';
$classes[] = 'Symfony\Component\Routing\Loader\XmlFileLoader';
$classes[] = 'Symfony\Component\HttpKernel\Config\FileLocator';
$classes[] = 'Symfony\Component\Routing\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\GlobFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\DirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\ContainerLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\AttributeRouteControllerLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeDirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\Psr4DirectoryLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Secrets\SodiumVault';
$classes[] = 'Symfony\Component\String\LazyString';
$classes[] = 'Symfony\Component\Security\Http\Firewall\AccessListener';
$classes[] = 'Symfony\Component\Security\Http\AccessMap';
$classes[] = 'Symfony\Component\Security\Http\Session\SessionAuthenticationStrategy';
$classes[] = 'Symfony\Component\Security\Core\Authentication\AuthenticationTrustResolver';
$classes[] = 'Symfony\Component\Security\Http\Authentication\AuthenticationUtils';
$classes[] = 'Symfony\Component\Security\Http\Authenticator\FormLoginAuthenticator';
$classes[] = 'Symfony\Component\Security\Http\Authentication\DefaultAuthenticationSuccessHandler';
$classes[] = 'Symfony\Component\Security\Http\Authentication\DefaultAuthenticationFailureHandler';
$classes[] = 'Symfony\Component\Security\Http\Authentication\CustomAuthenticationSuccessHandler';
$classes[] = 'App\Security\LoginSuccessHandler';
$classes[] = 'Symfony\Component\Security\Http\Authentication\AuthenticatorManager';
$classes[] = 'Symfony\Component\Security\Http\Authenticator\RememberMeAuthenticator';
$classes[] = 'Symfony\Component\Security\Http\RememberMe\SignatureRememberMeHandler';
$classes[] = 'Symfony\Component\Security\Core\Signature\SignatureHasher';
$classes[] = 'Symfony\Component\Security\Core\Authorization\AuthorizationChecker';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ChannelListener';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ContextListener';
$classes[] = 'Symfony\Component\Security\Csrf\CsrfTokenManager';
$classes[] = 'Symfony\Component\Security\Csrf\TokenGenerator\UriSafeTokenGenerator';
$classes[] = 'Symfony\Component\Security\Csrf\TokenStorage\SessionTokenStorage';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallMap';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\LazyFirewallContext';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ExceptionListener';
$classes[] = 'Symfony\Component\Security\Http\Firewall\LogoutListener';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallConfig';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallContext';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security';
$classes[] = 'Symfony\Component\Security\Http\HttpUtils';
$classes[] = 'Symfony\Component\Security\Http\EventListener\UserProviderListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CheckCredentialsListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CheckRememberMeConditionsListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CsrfProtectionListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\PasswordMigratingListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\RememberMeListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\SessionStrategyListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\UserCheckerListener';
$classes[] = 'Symfony\Component\Security\Core\User\ChainUserProvider';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CookieClearingLogoutListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CsrfTokenClearingLogoutListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\DefaultLogoutListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\SessionLogoutListener';
$classes[] = 'Symfony\Component\Security\Http\Logout\LogoutUrlGenerator';
$classes[] = 'Symfony\Component\PasswordHasher\Hasher\PasswordHasherFactory';
$classes[] = 'Symfony\Component\Security\Http\RememberMe\ResponseListener';
$classes[] = 'Symfony\Component\Security\Core\Role\RoleHierarchy';
$classes[] = 'Symfony\Bundle\SecurityBundle\Routing\LogoutRouteLoader';
$classes[] = 'Symfony\Component\Security\Core\Authentication\Token\Storage\UsageTrackingTokenStorage';
$classes[] = 'Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage';
$classes[] = 'Symfony\Bridge\Doctrine\Security\User\EntityUserProvider';
$classes[] = 'Symfony\Component\Security\Core\User\InMemoryUserChecker';
$classes[] = 'Symfony\Component\PasswordHasher\Hasher\UserPasswordHasher';
$classes[] = 'Symfony\Component\Security\Core\Validator\Constraints\UserPasswordValidator';
$classes[] = 'Symfony\Component\Serializer\DataCollector\SerializerDataCollector';
$classes[] = 'Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\LoaderChain';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\AttributeLoader';
$classes[] = 'Symfony\Component\DependencyInjection\ContainerInterface';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\ServicesResetter';
$classes[] = 'Symfony\Component\HttpFoundation\Session\SessionFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\NativeSessionStorageFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\MetadataBag';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\Handler\StrictSessionHandler';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\SessionListener';
$classes[] = 'Symfony\Component\String\Slugger\AsciiSlugger';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\ControllersMapGenerator';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\AutoImportLocator';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\StimulusLoaderJavaScriptCompiler';
$classes[] = 'Symfony\UX\StimulusBundle\Ux\UxPackageReader';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\UxControllersTwigRuntime';
$classes[] = 'Symfony\Component\Translation\Loader\CsvFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuDatFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IniFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\JsonFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\MoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\QtFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuResFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\XliffFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Translation\LocaleSwitcher';
$classes[] = 'Symfony\Component\Translation\DataCollectorTranslator';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Translation\Translator';
$classes[] = 'Symfony\Component\Translation\Formatter\MessageFormatter';
$classes[] = 'Symfony\Component\Translation\IdentityTranslator';
$classes[] = 'Symfony\UX\Turbo\Doctrine\BroadcastListener';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\TwigBroadcaster';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\ImuxBroadcaster';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\IdAccessor';
$classes[] = 'Symfony\UX\Turbo\Request\RequestListener';
$classes[] = 'Symfony\UX\Turbo\Twig\TurboRuntime';
$classes[] = 'Twig\Cache\FilesystemCache';
$classes[] = 'Twig\Extension\CoreExtension';
$classes[] = 'Twig\Extension\EscaperExtension';
$classes[] = 'Twig\Extension\OptimizerExtension';
$classes[] = 'Twig\Extension\StagingExtension';
$classes[] = 'Twig\ExtensionSet';
$classes[] = 'Twig\Template';
$classes[] = 'Twig\TemplateWrapper';
$classes[] = 'Twig\Environment';
$classes[] = 'Twig\Loader\FilesystemLoader';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\DumpExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ProfilerExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\TranslationExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\AssetExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\CodeExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\RoutingExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\YamlExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\StopwatchExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ExpressionExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpFoundationExtension';
$classes[] = 'Symfony\Component\HttpFoundation\UrlHelper';
$classes[] = 'Symfony\Bridge\Twig\Extension\WebLinkExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\FormExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ImportMapExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\LogoutUrlExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\SecurityExtension';
$classes[] = 'Symfony\Component\Security\Http\Impersonate\ImpersonateUrlGenerator';
$classes[] = 'App\Twig\AssetExtension';
$classes[] = 'App\Service\AssetService';
$classes[] = 'App\Twig\ContentExtension';
$classes[] = 'App\Twig\PartnerExtension';
$classes[] = 'App\Twig\PromotionalBannerExtension';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Twig\DoctrineExtension';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Twig\WebProfilerExtension';
$classes[] = 'Symfony\Component\VarDumper\Dumper\HtmlDumper';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\UxControllersTwigExtension';
$classes[] = 'Symfony\UX\Turbo\Twig\TwigExtension';
$classes[] = 'Vich\UploaderBundle\Twig\Extension\UploaderExtension';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\StimulusTwigExtension';
$classes[] = 'Symfony\UX\StimulusBundle\Helper\StimulusHelper';
$classes[] = 'Symfony\Bridge\Twig\AppVariable';
$classes[] = 'Twig\RuntimeLoader\ContainerRuntimeLoader';
$classes[] = 'Twig\Extra\TwigExtraBundle\MissingExtensionSuggestor';
$classes[] = 'Symfony\Bundle\TwigBundle\DependencyInjection\Configurator\EnvironmentConfigurator';
$classes[] = 'Symfony\Bridge\Twig\Form\TwigRendererEngine';
$classes[] = 'Symfony\Component\Form\FormRenderer';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageListener';
$classes[] = 'Symfony\Bridge\Twig\Mime\BodyRenderer';
$classes[] = 'Twig\Profiler\Profile';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelRuntime';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\LazyLoadingFragmentHandler';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\FragmentUriGenerator';
$classes[] = 'Symfony\Component\HttpFoundation\UriSigner';
$classes[] = 'Symfony\Bridge\Twig\Extension\ImportMapRuntime';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapRenderer';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfRuntime';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerRuntime';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ValidateRequestListener';
$classes[] = 'Symfony\Component\Validator\ValidatorBuilder';
$classes[] = 'Symfony\Component\Validator\Validation';
$classes[] = 'Symfony\Component\Validator\ContainerConstraintValidatorFactory';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\DoctrineInitializer';
$classes[] = 'Symfony\Component\Validator\Mapping\Loader\PropertyInfoLoader';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\DoctrineLoader';
$classes[] = 'Symfony\Component\Validator\Constraints\EmailValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\ExpressionValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\ExpressionLanguageProvider';
$classes[] = 'Symfony\Component\Validator\Constraints\NoSuspiciousCharactersValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\NotCompromisedPasswordValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\WhenValidator';
$classes[] = 'Symfony\Component\VarDumper\Cloner\VarCloner';
$classes[] = 'Symfony\Component\VarDumper\Server\Connection';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\SourceContextProvider';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\RequestContextProvider';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\CliContextProvider';
$classes[] = 'Vich\UploaderBundle\Adapter\ORM\DoctrineORMAdapter';
$classes[] = 'Vich\UploaderBundle\Handler\DownloadHandler';
$classes[] = 'Vich\UploaderBundle\Form\Type\VichFileType';
$classes[] = 'Vich\UploaderBundle\Form\Type\VichImageType';
$classes[] = 'Vich\UploaderBundle\EventListener\Doctrine\CleanListener';
$classes[] = 'Vich\UploaderBundle\EventListener\Doctrine\RemoveListener';
$classes[] = 'Vich\UploaderBundle\EventListener\Doctrine\UploadListener';
$classes[] = 'Vich\UploaderBundle\Metadata\MetadataReader';
$classes[] = 'Metadata\MetadataFactory';
$classes[] = 'Metadata\Driver\DriverChain';
$classes[] = 'Vich\UploaderBundle\Metadata\Driver\XmlDriver';
$classes[] = 'Metadata\Driver\FileLocator';
$classes[] = 'Vich\UploaderBundle\Metadata\Driver\AnnotationDriver';
$classes[] = 'Vich\UploaderBundle\Metadata\Driver\AttributeReader';
$classes[] = 'Vich\UploaderBundle\Metadata\Driver\YamlDriver';
$classes[] = 'Vich\UploaderBundle\Metadata\Driver\YmlDriver';
$classes[] = 'Metadata\Cache\FileCache';
$classes[] = 'Vich\UploaderBundle\Mapping\PropertyMappingFactory';
$classes[] = 'Vich\UploaderBundle\Mapping\PropertyMappingResolver';
$classes[] = 'Vich\UploaderBundle\Storage\FileSystemStorage';
$classes[] = 'Vich\UploaderBundle\Handler\UploadHandler';
$classes[] = 'Vich\UploaderBundle\Injector\FileInjector';
$classes[] = 'Symfony\Component\WebLink\EventListener\AddLinkHeaderListener';
$classes[] = 'Symfony\Component\WebLink\HttpHeaderSerializer';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\ExceptionPanelController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\ProfilerController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\RouterController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Csp\ContentSecurityPolicyHandler';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Csp\NonceGenerator';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\EventListener\WebDebugToolbarListener';

$preloaded = Preloader::preload($classes);

$classes = [];
$classes[] = 'Symfony\\Component\\Routing\\Generator\\CompiledUrlGenerator';
$classes[] = 'Symfony\\Bundle\\FrameworkBundle\\Routing\\RedirectableCompiledUrlMatcher';
$classes[] = 'Symfony\\Component\\Validator\\Mapping\\ClassMetadata';
$classes[] = 'Symfony\\Component\\Form\\Extension\\Validator\\Constraints\\Form';
$preloaded = Preloader::preload($classes, $preloaded);
