<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* base.html.twig */
class __TwigTemplate_7431f3557902625da75943d20437619c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'meta_keywords' => [$this, 'block_meta_keywords'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
            'importmap' => [$this, 'block_importmap'],
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "base.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "base.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>";
        // line 6
        yield from $this->unwrap()->yieldBlock('title', $context, $blocks);
        yield "</title>
    <meta name=\"description\" content=\"";
        // line 7
        yield from $this->unwrap()->yieldBlock('meta_description', $context, $blocks);
        yield "\">
    <meta name=\"keywords\" content=\"";
        // line 8
        yield from $this->unwrap()->yieldBlock('meta_keywords', $context, $blocks);
        yield "\">

    <!-- Professional Favicon System -->
    <!-- Standard favicon -->
    <link rel=\"icon\" type=\"image/png\" href=\"";
        // line 12
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">

    <!-- PNG favicons for different sizes -->
    <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"";
        // line 15
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">
    <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"";
        // line 16
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">

    <!-- Apple Touch Icon for iOS devices -->
    <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"";
        // line 19
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">

    <!-- Android Chrome icons -->
    <link rel=\"icon\" type=\"image/png\" sizes=\"192x192\" href=\"";
        // line 22
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">
    <link rel=\"icon\" type=\"image/png\" sizes=\"512x512\" href=\"";
        // line 23
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">

    <!-- Microsoft Windows tiles -->
    <meta name=\"msapplication-TileColor\" content=\"#081c2c\">
    <meta name=\"msapplication-config\" content=\"";
        // line 27
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("favicons/browserconfig.xml"), "html", null, true);
        yield "\">

    <!-- Theme colors for mobile browsers -->
    <meta name=\"theme-color\" content=\"#081c2c\">

    <!-- SEO Meta Tags -->
    ";
        // line 33
        yield from $this->load("components/seo_meta.html.twig", 33)->unwrap()->yield($context);
        // line 34
        yield "
    <!-- Bootstrap CSS -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <!-- Font Awesome Icons -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">

    <!-- Google Fonts -->
    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">
    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>
    <link href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap\" rel=\"stylesheet\">

    ";
        // line 46
        yield from $this->unwrap()->yieldBlock('stylesheets', $context, $blocks);
        // line 205
        yield "</head>
<body>
    <!-- Loading Spinner -->
    ";
        // line 208
        yield from $this->load("components/loading_spinner.html.twig", 208)->unwrap()->yield($context);
        // line 209
        yield "
    <!-- Promotional Banner (Above Navigation) -->
    ";
        // line 211
        yield from $this->load("components/promotional_banner.html.twig", 211)->unwrap()->yield($context);
        // line 212
        yield "
    <!-- Navigation -->
    <nav class=\"navbar navbar-expand-lg navbar-light shadow-lg sticky-top\" style=\"background-color: #ffffff; z-index: 1030;\">
        <div class=\"container\">
            <!-- Left-aligned Logo to match courses section -->
            <a class=\"navbar-brand d-flex align-items-center py-2\" href=\"";
        // line 217
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\" style=\"margin-left: 0;\">
                <div class=\"logo-container me-3\">
                    <!-- Use Horizontal Logo for topbar -->
                    <img src=\"";
        // line 220
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-horizontal.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"logo-img\" style=\"height: 40px; width: auto; max-width: 180px;\" onerror=\"this.src='";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "'\">
                </div>
            </a>

            <button class=\"navbar-toggler border-0 p-2\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\" aria-controls=\"navbarNav\" aria-expanded=\"false\" aria-label=\"Toggle navigation\">
                <span class=\"navbar-toggler-icon\"></span>
            </button>

            <div class=\"collapse navbar-collapse\" id=\"navbarNav\">
                <!-- Centered Navigation Menu -->
                <ul class=\"navbar-nav mx-auto\">
                    <li class=\"nav-item dropdown\">
                        <a class=\"nav-link dropdown-toggle px-3 mx-1 nav-link-hover\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 400;\">
                            Our Services
                        </a>
                        <ul class=\"dropdown-menu shadow border-0 rounded-3\">
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 236
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses_list");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-graduation-cap me-2\"></i>Trading Courses</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 237
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-video me-2\"></i>Trading Videos</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 238
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_webinar");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-broadcast-tower me-2\"></i>Live Trading Webinars</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"#\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chart-line me-2\"></i>Buy/Sell Signals</a></li>
                        </ul>
                    </li>
                    <li class=\"nav-item dropdown\">
                        <a class=\"nav-link dropdown-toggle px-3 mx-1 nav-link-hover\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 400;\">
                            Trading Tools
                        </a>
                        <ul class=\"dropdown-menu shadow border-0 rounded-3\">
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 247
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chart-bar me-2\"></i>Market Analysis</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"https://www.youtube.com/@capitolacademy1\" target=\"_blank\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fab fa-youtube me-2\"></i>YouTube Channel</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"#\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-calendar-alt me-2\"></i>Economic Calendar</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"#\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chart-line me-2\"></i>Live Charts</a></li>
                        </ul>
                    </li>
                    <li class=\"nav-item dropdown\">
                        <a class=\"nav-link dropdown-toggle px-3 mx-1 nav-link-hover\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 400;\">
                            Company
                        </a>
                        <ul class=\"dropdown-menu shadow border-0 rounded-3\">
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 258
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_about");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-info-circle me-2\"></i>About Us</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 259
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_instructors");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chalkboard-teacher me-2\"></i>Instructors</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 260
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_partnership");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-handshake me-2\"></i>Partners</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"";
        // line 261
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-envelope me-2\"></i>Contact Us</a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Enhanced Search Box with Autocomplete - Right aligned to match courses section -->
                <div class=\"d-flex align-items-center me-0 position-relative\" style=\"margin-right: 0;\">
                    <form class=\"d-flex me-2\" role=\"search\" action=\"";
        // line 268
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_search");
        yield "\" method=\"GET\">
                        <div class=\"search-container position-relative\">
                            <input class=\"form-control form-control-sm search-input-enhanced\"
                                   type=\"search\"
                                   name=\"q\"
                                   placeholder=\"Search...\"
                                   aria-label=\"Search\"
                                   style=\"width: 200px; border-radius: 6px; border: 2px solid #e0e0e0; padding: 8px 12px;\"
                                   autocomplete=\"off\"
                                   id=\"globalSearchInput\">
                            <div class=\"search-suggestions\" id=\"searchSuggestions\"></div>
                        </div>
                        <button class=\"btn btn-sm ms-1 search-btn-enhanced\" type=\"submit\" style=\"border-radius: 6px; border: 2px solid #011a2d; background: #011a2d; color: white; padding: 8px 12px;\">
                            <i class=\"fas fa-search\"></i>
                        </button>
                    </form>
                </div>

                <!-- Right-aligned buttons to match courses section margin -->
                <div class=\"d-flex gap-2\" style=\"margin-right: 0;\">
                    ";
        // line 288
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 288, $this->source); })()), "user", [], "any", false, false, false, 288)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 289
            yield "                        <!-- Professional User Profile Dropdown -->
                        <div class=\"dropdown user-profile-dropdown\">
                            <a class=\"btn btn-profile-dropdown dropdown-toggle d-flex align-items-center\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">
                                <!-- User Avatar -->
                                <div class=\"user-avatar-small me-2\">
                                    ";
            // line 294
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 294, $this->source); })()), "user", [], "any", false, false, false, 294), "profilePicture", [], "any", false, false, false, 294)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 295
                yield "                                        <img src=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 295, $this->source); })()), "user", [], "any", false, false, false, 295), "profilePicture", [], "any", false, false, false, 295))), "html", null, true);
                yield "\" alt=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 295, $this->source); })()), "user", [], "any", false, false, false, 295), "fullName", [], "any", false, false, false, 295), "html", null, true);
                yield "\" class=\"rounded-circle\">
                                    ";
            } else {
                // line 297
                yield "                                        <div class=\"user-initials-avatar\">
                                            ";
                // line 298
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 298, $this->source); })()), "user", [], "any", false, false, false, 298), "firstName", [], "any", false, false, false, 298))), "html", null, true);
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 298, $this->source); })()), "user", [], "any", false, false, false, 298), "lastName", [], "any", false, false, false, 298))), "html", null, true);
                yield "
                                        </div>
                                    ";
            }
            // line 301
            yield "                                </div>
                                <!-- User Name -->
                                <span class=\"user-name\">";
            // line 303
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 303, $this->source); })()), "user", [], "any", false, false, false, 303), "fullName", [], "any", false, false, false, 303), "html", null, true);
            yield "</span>
                            </a>
                            <ul class=\"dropdown-menu dropdown-menu-end user-dropdown-menu\">
                                <!-- Compact User Info Header -->
                                <li class=\"dropdown-header user-dropdown-header\">
                                    <div class=\"d-flex align-items-center\">
                                        <div class=\"user-avatar-compact me-2\">
                                            ";
            // line 310
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 310, $this->source); })()), "user", [], "any", false, false, false, 310), "profilePicture", [], "any", false, false, false, 310)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 311
                yield "                                                <img src=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 311, $this->source); })()), "user", [], "any", false, false, false, 311), "profilePicture", [], "any", false, false, false, 311))), "html", null, true);
                yield "\" alt=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 311, $this->source); })()), "user", [], "any", false, false, false, 311), "fullName", [], "any", false, false, false, 311), "html", null, true);
                yield "\" class=\"rounded-circle\">
                                            ";
            } else {
                // line 313
                yield "                                                <div class=\"user-initials-compact\">
                                                    ";
                // line 314
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 314, $this->source); })()), "user", [], "any", false, false, false, 314), "firstName", [], "any", false, false, false, 314))), "html", null, true);
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 314, $this->source); })()), "user", [], "any", false, false, false, 314), "lastName", [], "any", false, false, false, 314))), "html", null, true);
                yield "
                                                </div>
                                            ";
            }
            // line 317
            yield "                                        </div>
                                        <div class=\"user-info-compact\">
                                            <div class=\"user-name-compact\">";
            // line 319
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 319, $this->source); })()), "user", [], "any", false, false, false, 319), "fullName", [], "any", false, false, false, 319), "html", null, true);
            yield "</div>
                                            <small class=\"user-email-compact\">";
            // line 320
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 320, $this->source); })()), "user", [], "any", false, false, false, 320), "email", [], "any", false, false, false, 320), "html", null, true);
            yield "</small>
                                        </div>
                                    </div>
                                </li>

                                <!-- Profile Option -->
                                <li>
                                    <a class=\"dropdown-item dropdown-item-capitol\" href=\"";
            // line 327
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_profile");
            yield "\">
                                        <i class=\"fas fa-user-circle me-2\"></i>
                                        <span>My Profile</span>
                                    </a>
                                </li>

                                <!-- Sign Out Option -->
                                <li>
                                    <a class=\"dropdown-item dropdown-item-capitol dropdown-item-logout\" href=\"";
            // line 335
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_logout");
            yield "\">
                                        <i class=\"fas fa-sign-out-alt me-2\"></i>
                                        <span>Sign Out</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    ";
        } else {
            // line 343
            yield "                        <!-- Enhanced Guest User Menu -->
                        <a href=\"";
            // line 344
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_register");
            yield "\" class=\"btn btn-primary btn-enhanced-topbar me-2\" style=\"
                            background-color: #011a2d;
                            border: 2px solid #011a2d;
                            color: white;
                            font-weight: 600;
                            border-radius: 6px;
                            padding: 8px 20px;
                            transition: all 0.3s ease;
                            text-decoration: none;
                        \" onmouseover=\"this.style.backgroundColor='#dc3545'; this.style.borderColor='#dc3545'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(220, 53, 69, 0.3)'\"
                           onmouseout=\"this.style.backgroundColor='#011a2d'; this.style.borderColor='#011a2d'; this.style.transform='translateY(0)'; this.style.boxShadow='none'\">
                            <i class=\"fas fa-user-plus me-2\"></i>Join for Free
                        </a>
                        <a href=\"";
            // line 357
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
            yield "\" class=\"btn btn-outline-dark btn-enhanced-topbar\" style=\"
                            border: 2px solid #011a2d;
                            color: #011a2d;
                            border-radius: 6px;
                            padding: 8px 20px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            text-decoration: none;
                        \" onmouseover=\"this.style.backgroundColor='#011a2d'; this.style.color='white'\"
                           onmouseout=\"this.style.backgroundColor='transparent'; this.style.color='#011a2d'\">
                            <i class=\"fas fa-sign-in-alt me-2\"></i>Login
                        </a>
                    ";
        }
        // line 370
        yield "                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    ";
        // line 376
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 376, $this->source); })()), "flashes", [], "any", false, false, false, 376));
        foreach ($context['_seq'] as $context["type"] => $context["messages"]) {
            // line 377
            yield "        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable($context["messages"]);
            foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
                // line 378
                yield "            <div class=\"alert alert-";
                yield ((($context["type"] == "error")) ? ("danger") : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["type"], "html", null, true)));
                yield " alert-dismissible fade show\" role=\"alert\">
                ";
                // line 379
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
                yield "
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
            </div>
        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 383
            yield "    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['type'], $context['messages'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 384
        yield "
    <!-- Main Content -->
    <main>
        ";
        // line 387
        yield from $this->unwrap()->yieldBlock('body', $context, $blocks);
        // line 388
        yield "    </main>

    <!-- Footer -->
    <footer class=\"py-4\" style=\"background: url('";
        // line 391
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Background 2 et bas de page HP.png"), "html", null, true);
        yield "') center/cover; color: #2c3e50; position: relative; margin-top: 6rem; width: 100%;\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- Left Column -->
                <div class=\"col-md-6\">
                    <!-- Capitol Academy Logo -->
                    <div class=\"mb-4\">
                        <img src=\"";
        // line 398
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"mb-3\" style=\"height: 100px; width: 100px; border-radius: 50%;\" onerror=\"this.src='";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/placeholders/image-placeholder.png"), "html", null, true);
        yield "'\">
                    </div>

                    <!-- Social Media Icons -->
                    <div class=\"mb-4\">
                        <div class=\"d-flex flex-wrap gap-2\">
                            <a href=\"https://www.facebook.com/CapitolAcademyTunisie/\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Follow us on Facebook\">
                                <i class=\"fab fa-facebook-f\"></i>
                            </a>
                            <a href=\"https://www.instagram.com/capitol_academy_global/\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Follow us on Instagram\">
                                <i class=\"fab fa-instagram\"></i>
                            </a>
                            <a href=\"https://www.youtube.com/@capitolacademy1\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Subscribe to our YouTube Channel\">
                                <i class=\"fab fa-youtube\"></i>
                            </a>
                            <a href=\"https://tn.linkedin.com/company/capitol-academy-tunisie\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Connect with us on LinkedIn\">
                                <i class=\"fab fa-linkedin-in\"></i>
                            </a>
                            <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Contact us on WhatsApp\">
                                <i class=\"fab fa-whatsapp\"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Legal Links -->
                    <div class=\"mb-3\">
                        <a href=\"#\" class=\"text-decoration-none small footer-link me-3 fw-bold\" style=\"color: #2c3e50;\">Terms</a>
                        <a href=\"#\" class=\"text-decoration-none small footer-link me-3 fw-bold\" style=\"color: #2c3e50;\">Privacy Policy</a>
                        <a href=\"#\" class=\"text-decoration-none small footer-link me-3 fw-bold\" style=\"color: #2c3e50;\">Cookie Policy</a>
                        <a href=\"#\" class=\"text-decoration-none small footer-link fw-bold\" style=\"color: #2c3e50;\">Editorial Guidelines</a>
                    </div>
                </div>

                <!-- Right Column Navigation -->
                <div class=\"col-md-6\">
                    <div class=\"row\">
                        <div class=\"col-md-4\">
                            <h6 class=\"fw-bold mb-3\" style=\"color: #2c3e50;\">Our Services</h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\"><a href=\"";
        // line 437
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos");
        yield "\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Trading Videos</a></li>
                                <li class=\"mb-2\"><a href=\"";
        // line 438
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_webinar");
        yield "\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Live Trading Webinars</a></li>
                                <li class=\"mb-2\"><a href=\"#\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Buy/Sell Signals</a></li>
                            </ul>
                        </div>
                        <div class=\"col-md-4\">
                            <h6 class=\"fw-bold mb-3\" style=\"color: #2c3e50;\">Trading Tools</h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\"><a href=\"";
        // line 445
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Market Analysis</a></li>
                                <li class=\"mb-2\"><a href=\"https://www.youtube.com/@capitolacademy1\" target=\"_blank\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Youtube Channel</a></li>
                                <li class=\"mb-2\"><a href=\"#\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Economic Calendar</a></li>
                                <li class=\"mb-2\"><a href=\"#\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Live Charts</a></li>
                            </ul>
                        </div>
                        <div class=\"col-md-4\">
                            <h6 class=\"fw-bold mb-3\" style=\"color: #2c3e50;\">Company</h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\"><a href=\"";
        // line 454
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_about");
        yield "\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">About us</a></li>
                                <li class=\"mb-2\"><a href=\"";
        // line 455
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_instructors");
        yield "\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Instructors</a></li>
                                <li class=\"mb-2\"><a href=\"";
        // line 456
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_partnership");
        yield "\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Partners</a></li>
                                <li class=\"mb-2\"><a href=\"";
        // line 457
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <hr class=\"my-4\" style=\"border-color: rgba(44,62,80,0.3);\">

            <!-- Bottom Section -->
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Full Disclaimer Text -->
                    <div class=\"legal-disclaimer mb-4\">
                        <p class=\"mb-3\" style=\"color: #2c3e50; font-size: 0.8rem; line-height: 1.5;\">
                            <strong>Risk Warning:</strong> Trading financial instruments carries a high level of risk and may not be suitable for all investors. The high degree of leverage can work against you as well as for you. Before deciding to trade, you should carefully consider your investment objectives, level of experience, and risk appetite. There is a possibility that you may sustain a loss of some or all of your initial investment and therefore you should not invest money that you cannot afford to lose. You should be aware of all the risks associated with trading and seek advice from an independent financial advisor if you have any doubts.
                            <br><br>
                            All content on this website is for educational purposes only and should not be considered as financial advice. All trading involves risk and past performance is not indicative of future results. The information provided does not constitute investment advice and we strongly recommend that you seek independent financial advice before making any investment decisions.
                            <br><br>
                            Capitol Academy provides educational content and training materials for informational purposes only. We do not guarantee the accuracy, completeness, or usefulness of any information provided. Any reliance you place on such information is strictly at your own risk. Capitol Academy disclaims all liability and responsibility arising from any reliance placed on such materials by you or any other visitor to the website, or by anyone who may be informed of any of its contents.
                        </p>
                    </div>

                    <!-- reCAPTCHA Notice -->
                    <div class=\"text-center mb-2\">
                        <p class=\"mb-0\" style=\"color: #2c3e50; font-size: 0.75rem;\">
                            This site is protected by reCAPTCHA and the Google
                            <a href=\"https://policies.google.com/privacy\" style=\"color: #2c3e50;\">Privacy Policy</a> and
                            <a href=\"https://policies.google.com/terms\" style=\"color: #2c3e50;\">Terms of Service</a> apply.
                        </p>
                    </div>

                    <!-- Copyright -->
                    <div class=\"text-center\">
                        <p class=\"mb-0\" style=\"color: #2c3e50; font-size: 0.75rem;\">&copy; 2025 Capitol Academy. All rights reserved.</p>
                        <small style=\"color: #2c3e50; opacity: 0.8; font-size: 0.7rem;\">Registration #1264639G</small>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <!-- Global Search Autocomplete -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('globalSearchInput');
        const suggestionsContainer = document.getElementById('searchSuggestions');
        let currentSuggestions = [];
        let selectedIndex = -1;
        let searchTimeout;

        if (!searchInput || !suggestionsContainer) return;

        // Handle input events
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                hideSuggestions();
                return;
            }

            searchTimeout = setTimeout(() => {
                fetchSuggestions(query);
            }, 300);
        });

        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            if (!suggestionsContainer.classList.contains('show')) return;

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, currentSuggestions.length - 1);
                    updateSelection();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    updateSelection();
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && currentSuggestions[selectedIndex]) {
                        window.location.href = currentSuggestions[selectedIndex].url;
                    } else {
                        this.closest('form').submit();
                    }
                    break;
                case 'Escape':
                    hideSuggestions();
                    break;
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                hideSuggestions();
            }
        });

        function fetchSuggestions(query) {
            fetch(`";
        // line 566
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("api_search_autocomplete");
        yield "?q=\${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.suggestions) {
                        currentSuggestions = data.suggestions;
                        displaySuggestions(data.suggestions);
                    }
                })
                .catch(error => {
                    console.error('Search error:', error);
                });
        }

        function displaySuggestions(suggestions) {
            if (suggestions.length === 0) {
                hideSuggestions();
                return;
            }

            // Sort suggestions to show priority items first
            const sortedSuggestions = suggestions.sort((a, b) => {
                if (a.priority && !b.priority) return -1;
                if (!a.priority && b.priority) return 1;
                return 0;
            });

            suggestionsContainer.innerHTML = sortedSuggestions.map((suggestion, index) => `
                <div class=\"suggestion-item \${suggestion.priority ? 'priority-suggestion' : ''}\" data-index=\"\${index}\" data-url=\"\${suggestion.url}\">
                    <i class=\"\${suggestion.icon} suggestion-icon\"></i>
                    <div class=\"suggestion-content\">
                        <div class=\"suggestion-title\">\${suggestion.title}</div>
                        <div class=\"suggestion-category\">\${suggestion.category}</div>
                    </div>
                </div>
            `).join('');

            // Add click handlers
            suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', function() {
                    window.location.href = this.dataset.url;
                });
            });

            selectedIndex = -1;
            suggestionsContainer.classList.add('show');
        }

        function updateSelection() {
            const items = suggestionsContainer.querySelectorAll('.suggestion-item');
            items.forEach((item, index) => {
                item.classList.toggle('active', index === selectedIndex);
            });
        }

        function hideSuggestions() {
            suggestionsContainer.classList.remove('show');
            selectedIndex = -1;
        }
    });
    </script>

    <!-- Enhanced Dropdown Functionality with Auto-Close -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all Bootstrap dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // Enhanced dropdown behavior for navbar with auto-close on mouse leave
            const navDropdowns = document.querySelectorAll('.navbar-nav .dropdown');

            navDropdowns.forEach(dropdown => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                let closeTimeout;

                // Function to close dropdown
                function closeDropdown() {
                    menu.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }

                // Function to open dropdown
                function openDropdown() {
                    // Clear any pending close timeout
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Close other dropdowns
                    navDropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            const otherMenu = otherDropdown.querySelector('.dropdown-menu');
                            const otherToggle = otherDropdown.querySelector('.dropdown-toggle');
                            otherMenu.classList.remove('show');
                            otherToggle.setAttribute('aria-expanded', 'false');
                        }
                    });

                    // Open current dropdown
                    menu.classList.add('show');
                    toggle.setAttribute('aria-expanded', 'true');
                }

                // Add click event for better mobile support
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();

                    const isOpen = menu.classList.contains('show');
                    if (isOpen) {
                        closeDropdown();
                    } else {
                        openDropdown();
                    }
                });

                // Mouse enter event - open dropdown
                dropdown.addEventListener('mouseenter', function(e) {
                    // Clear any pending close timeout
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Only auto-open on desktop (screen width > 768px)
                    if (window.innerWidth > 768) {
                        openDropdown();
                    }
                });

                // Mouse leave event - close dropdown with delay
                dropdown.addEventListener('mouseleave', function(e) {
                    // Only auto-close on desktop and if dropdown is open
                    if (window.innerWidth > 768 && menu.classList.contains('show')) {
                        // Set timeout to close dropdown after 250ms delay
                        closeTimeout = setTimeout(() => {
                            closeDropdown();
                            closeTimeout = null;
                        }, 250);
                    }
                });

                // Mouse enter on dropdown menu - cancel close timeout
                menu.addEventListener('mouseenter', function(e) {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdown.contains(e.target)) {
                        closeDropdown();
                    }
                });

                // Close dropdown when clicking on menu items
                menu.addEventListener('click', function(e) {
                    if (e.target.classList.contains('dropdown-item')) {
                        closeDropdown();
                    }
                });

                // Handle window resize to ensure proper behavior
                window.addEventListener('resize', function() {
                    // Clear timeout on resize
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Close all dropdowns on mobile
                    if (window.innerWidth <= 768) {
                        closeDropdown();
                    }
                });
            });

            // Enhanced user profile dropdown behavior
            const userDropdowns = document.querySelectorAll('.user-profile-dropdown');

            userDropdowns.forEach(dropdown => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                let closeTimeout;

                // Function to close dropdown
                function closeDropdown() {
                    menu.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }

                // Function to open dropdown
                function openDropdown() {
                    // Clear any pending close timeout
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Close other dropdowns
                    userDropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            const otherMenu = otherDropdown.querySelector('.dropdown-menu');
                            const otherToggle = otherDropdown.querySelector('.dropdown-toggle');
                            otherMenu.classList.remove('show');
                            otherToggle.setAttribute('aria-expanded', 'false');
                        }
                    });

                    // Open current dropdown
                    menu.classList.add('show');
                    toggle.setAttribute('aria-expanded', 'true');

                    // Fix dropdown positioning to ensure it stays within viewport
                    setTimeout(() => {
                        const rect = menu.getBoundingClientRect();
                        const viewportWidth = window.innerWidth;

                        // Reset any previous transforms
                        menu.style.transform = '';

                        // If dropdown extends beyond right edge of viewport
                        if (rect.right > viewportWidth - 10) {
                            const overflow = rect.right - viewportWidth + 20; // 20px margin
                            menu.style.transform = `translateX(-\${overflow}px)`;
                        }

                        // If dropdown extends beyond left edge of viewport
                        if (rect.left < 10) {
                            menu.style.transform = `translateX(\${Math.abs(rect.left) + 20}px)`;
                        }
                    }, 10);
                }

                // Click event for dropdown toggle
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const isOpen = menu.classList.contains('show');
                    if (isOpen) {
                        closeDropdown();
                    } else {
                        openDropdown();
                    }
                });

                // Mouse enter event - open dropdown (desktop only)
                dropdown.addEventListener('mouseenter', function(e) {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    if (window.innerWidth > 768) {
                        openDropdown();
                    }
                });

                // Mouse leave event - close dropdown with 250ms delay
                dropdown.addEventListener('mouseleave', function(e) {
                    if (window.innerWidth > 768 && menu.classList.contains('show')) {
                        closeTimeout = setTimeout(() => {
                            closeDropdown();
                            closeTimeout = null;
                        }, 250);
                    }
                });

                // Mouse enter on dropdown menu - cancel close timeout
                menu.addEventListener('mouseenter', function(e) {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdown.contains(e.target)) {
                        closeDropdown();
                    }
                });

                // Close dropdown when clicking on menu items
                menu.addEventListener('click', function(e) {
                    if (e.target.classList.contains('dropdown-item') || e.target.closest('.dropdown-item')) {
                        closeDropdown();
                    }
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    if (window.innerWidth <= 768) {
                        closeDropdown();
                    }
                });
            });
        });
    </script>

    <!-- Enhanced User Experience Scripts -->
    <script>
        // Handle offline/online status
        window.addEventListener('online', function() {
            console.log('Capitol Academy: Back online');
            // Remove offline indicator if exists
            const offlineIndicator = document.getElementById('offline-indicator');
            if (offlineIndicator) {
                offlineIndicator.remove();
            }
        });

        window.addEventListener('offline', function() {
            console.log('Capitol Academy: Gone offline');
            // Show offline indicator
            const offlineIndicator = document.createElement('div');
            offlineIndicator.id = 'offline-indicator';
            offlineIndicator.innerHTML = '<i class=\"fas fa-wifi me-2\"></i>You are currently offline';
            offlineIndicator.className = 'alert alert-warning position-fixed';
            offlineIndicator.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; margin: 0;';
            document.body.appendChild(offlineIndicator);
        });
    </script>

    ";
        // line 899
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        // line 902
        yield "
    <!-- Enhanced Navigation and UI Styles -->
    <style>
        /* Capitol Academy Professional Navigation */
        .navbar {
            background-color: #ffffff !important;
            border-bottom: 2px solid rgba(8, 28, 44, 0.1);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 15px rgba(8, 28, 44, 0.1);
            position: sticky !important;
            top: 0 !important;
            z-index: 1050 !important;
            width: 100% !important;
        }

        .capitol-navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Logo and Brand Styling */
        .logo-container {
            /* Clean logo container with no visual effects */
        }

        .logo-img {
            height: 50px;
            width: auto;
            max-width: 200px;
            display: block;
        }

        /* Mobile logo adjustments */
        @media (max-width: 767.98px) {
            .logo-img {
                height: 40px;
                max-width: 40px;
            }
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: 800;
            color: #fff;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
            letter-spacing: 0.8px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-tagline {
            font-size: 0.8rem;
            color: #dc3545;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Navigation Enhancements */
        .navbar {
            padding: 0.8rem 0;
            min-height: 80px;
            position: relative;
        }

        .nav-link-hover {
            transition: all 0.3s ease;
            position: relative;
            font-weight: 500;
            font-size: 1rem;
            padding: 0.75rem 1rem !important;
            border-radius: 6px;
            margin: 0 0.25rem;
            color: #011a2d !important;
        }

        .nav-link-hover:hover {
            background-color: rgba(30, 60, 114, 0.08) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15);
            color: #1e3c72 !important;
        }

        .nav-link-hover:focus {
            background-color: rgba(30, 60, 114, 0.08) !important;
            color: #1e3c72 !important;
            box-shadow: 0 0 0 2px rgba(30, 60, 114, 0.25);
            outline: none;
        }

        .nav-link-hover:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(30, 60, 114, 0.2);
        }

        .dropdown-menu {
            animation: fadeInDown 0.3s ease;
            border: 1px solid rgba(30, 60, 114, 0.1);
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            margin-top: 0.25rem;
            background: #ffffff;
            z-index: 1060 !important;
            min-width: 200px;
        }

        .dropdown-menu.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .dropdown-item {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 10px;
            margin: 0 1rem;
            padding: 12px 20px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .dropdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .dropdown-item:hover::before {
            left: 100%;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            transform: translateX(8px) scale(1.02);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
        }

        .dropdown-item:active {
            background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
            color: white;
            transform: translateX(8px) scale(0.98);
        }

        .dropdown-toggle::after {
            transition: transform 0.3s ease;
        }

        .dropdown-toggle[aria-expanded=\"true\"]::after {
            transform: rotate(180deg);
        }

        .nav-item.dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        @media (min-width: 992px) {
            .navbar-nav .dropdown:hover .dropdown-menu {
                display: block;
                animation: fadeInDown 0.3s ease;
            }
        }

        .btn-hover-effect {
            transition: all 0.3s ease;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Social Media Button Enhancements */
        .social-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .social-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .social-btn.btn-outline-light:hover {
            background-color: #fff;
            color: #333;
        }

        /* Enhanced Search Box Styling */
        .search-input-enhanced {
            border: 2px solid #011a2d !important;
            border-radius: 25px !important;
            padding: 8px 16px !important;
            color: #011a2d !important;
            background-color: white !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
        }

        .search-input-enhanced:focus {
            border-color: #011a2d !important;
            box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
            outline: none !important;
            transform: translateY(-1px) !important;
        }

        .search-input-enhanced::placeholder {
            color: rgba(1, 26, 45, 0.6) !important;
        }

        .search-btn-enhanced {
            background-color: #011a2d !important;
            border: 2px solid #011a2d !important;
            color: white !important;
            border-radius: 25px !important;
            padding: 8px 12px !important;
            transition: all 0.3s ease !important;
            font-weight: 600 !important;
        }

        .search-btn-enhanced:hover {
            background-color: #a90418 !important;
            border-color: #a90418 !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3) !important;
        }

        /* Search Suggestions Dropdown */
        .search-container {
            position: relative;
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.15);
            z-index: 1050;
            max-height: 400px;
            overflow-y: auto;
            display: none;
        }

        .search-suggestions.show {
            display: block;
        }

        .suggestion-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .suggestion-item.active {
            background-color: #011a2d;
            color: white;
        }

        .suggestion-icon {
            color: #011a2d;
            width: 16px;
            text-align: center;
        }

        .suggestion-item.active .suggestion-icon {
            color: white;
        }

        .suggestion-content {
            flex: 1;
        }

        .suggestion-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .suggestion-category {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .suggestion-item.active .suggestion-category {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Priority suggestions styling */
        .priority-suggestion {
            background-color: #f0f8ff;
            border-left: 4px solid #011a2d;
            font-weight: 600;
        }

        .priority-suggestion:hover {
            background-color: #e6f3ff;
        }

        .priority-suggestion.active {
            background-color: #011a2d;
            color: white;
        }

        .priority-suggestion .suggestion-icon {
            color: #dc3545;
        }

        .priority-suggestion.active .suggestion-icon {
            color: white;
        }

        /* Capitol Academy Enhanced Topbar Buttons */
        .btn-enhanced-topbar {
            border-radius: 6px !important;
            padding: 10px 20px !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            border-width: 2px !important;
            font-size: 14px !important;
            letter-spacing: 0.5px !important;
        }

        .btn-enhanced-topbar:hover {
            transform: translateY(-2px) !important;
        }

        /* Join for Free Button - Capitol Academy Dark Blue with Red Hover */
        .btn-enhanced-topbar.btn-success {
            background-color: #011a2d !important;
            border-color: #011a2d !important;
            color: white !important;
        }

        .btn-enhanced-topbar.btn-success:hover {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4) !important;
        }

        /* Login Button - Outline Dark Blue */
        .btn-enhanced-topbar.btn-outline-dark {
            color: #011a2d !important;
            border-color: #011a2d !important;
            background-color: transparent !important;
        }

        .btn-enhanced-topbar.btn-outline-dark:hover {
            background-color: #011a2d !important;
            border-color: #011a2d !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.3) !important;
        }

        .social-btn.btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .social-btn.btn-outline-info:hover {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: #fff;
        }

        /* Footer Link Enhancements */
        .footer-link {
            transition: all 0.2s ease;
            text-decoration: none;
            color: #2c3e50 !important;
        }

        .footer-link:hover {
            color: #a90418 !important;
            text-decoration: underline;
        }

        /* Social media icons hover effects - Ensure footer icons are always circular */
        .footer-social-btn {
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            border: 2px solid #2c3e50 !important;
            color: #2c3e50 !important;
            background: transparent !important;
        }

        .footer-social-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
            background: #2c3e50 !important;
            border-color: #2c3e50 !important;
            color: white !important;
        }

        .footer-social-btn i {
            font-size: 1rem !important;
            color: inherit !important;
        }

        /* General social media icons for other contexts */
        .social-btn {
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .social-btn:hover {
            background-color: #2c3e50 !important;
            border-color: #2c3e50 !important;
            color: white !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
        }

        /* Form Button Enhancements */
        .btn-enhanced {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-weight: 600;
            letter-spacing: 0.5px;
            border-width: 2px;
        }

        .btn-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .btn-enhanced:hover::before {
            left: 100%;
        }

        .btn-enhanced:active {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Form Container Enhancements */
        .form-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Animation Keyframes */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Capitol Academy Professional Profile Button */
        .user-profile-dropdown .btn-profile-dropdown {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border: 2px solid rgba(30, 60, 114, 0.2);
            color: white;
            padding: 8px 14px;
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(30, 60, 114, 0.25);
            position: relative;
            overflow: hidden;
        }

        .user-profile-dropdown .btn-profile-dropdown::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .user-profile-dropdown .btn-profile-dropdown:hover::before {
            left: 100%;
        }

        .user-profile-dropdown .btn-profile-dropdown:hover {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-color: rgba(220, 53, 69, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
            color: white;
        }

        .user-profile-dropdown .btn-profile-dropdown:focus {
            box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.25);
            color: white;
            outline: none;
        }

        .user-profile-dropdown .btn-profile-dropdown:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
        }

        /* User Avatar Styles */
        .user-avatar-small {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-avatar-small img {
            width: 30px;
            height: 30px;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(30, 60, 114, 0.2);
        }

        .user-avatar-dropdown {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-avatar-dropdown img {
            width: 48px;
            height: 48px;
            object-fit: cover;
            border: 2px solid #e9ecef;
        }

        .user-initials-avatar {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 11px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 6px rgba(220, 53, 69, 0.2);
        }

        .user-initials-avatar-large {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            border: 2px solid #e9ecef;
        }

        .user-name {
            font-size: 13px;
            font-weight: 600;
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Capitol Academy Professional User Dropdown Menu */
        .user-dropdown-menu {
            min-width: 240px;
            border: 2px solid rgba(30, 60, 114, 0.1);
            border-radius: 8px;
            padding: 0;
            margin-top: 6px;
            background: white;
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.15);
            animation: fadeInDown 0.3s ease;
            /* Fix positioning issues */
            position: absolute !important;
            right: 0 !important;
            left: auto !important;
            transform: none !important;
            /* Ensure dropdown stays within viewport */
            max-width: calc(100vw - 20px);
            overflow: hidden;
        }

        /* Compact User Header */
        .user-dropdown-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 6px 6px 0 0;
            padding: 12px 16px;
            border-bottom: 2px solid rgba(220, 53, 69, 0.2);
            margin: 0;
        }

        .user-avatar-compact {
            width: 32px;
            height: 32px;
            flex-shrink: 0;
        }

        .user-avatar-compact img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 2px solid white;
        }

        .user-initials-compact {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            border: 2px solid white;
        }

        .user-info-compact {
            min-width: 0;
            flex: 1;
        }

        .user-name-compact {
            font-weight: 600;
            font-size: 14px;
            color: white;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-email-compact {
            color: rgba(255, 255, 255, 0.8);
            font-size: 11px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Capitol Academy Dropdown Items */
        .dropdown-item-capitol {
            padding: 10px 16px;
            transition: all 0.3s ease;
            border-radius: 0;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: #011a2d;
            border: none;
            background: transparent;
        }

        .dropdown-item-capitol:hover {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            transform: translateX(3px);
            padding-left: 20px;
        }

        .dropdown-item-capitol:focus {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.3);
            outline: none;
        }

        .dropdown-item-logout:hover {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
        }

        .dropdown-item-logout:focus {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
            box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .dropdown-item-capitol i {
            width: 16px;
            text-align: center;
            font-size: 13px;
        }



        /* Dropdown positioning fix for edge cases */
        .user-profile-dropdown {
            position: relative;
        }

        .user-profile-dropdown .dropdown-menu {
            /* Ensure proper positioning */
            inset: auto 0px auto auto !important;
            transform: translate3d(0px, 42px, 0px) !important;
        }

        /* Handle viewport edge cases */
        @media (max-width: 320px) {
            .user-dropdown-menu {
                min-width: 250px;
                right: -10px !important;
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .navbar-brand span {
                display: none !important;
            }

            .social-btn {
                width: 35px;
                height: 35px;
            }

            .user-name {
                display: none;
            }

            .user-dropdown-menu {
                min-width: 260px;
                /* Better mobile positioning */
                right: 10px !important;
                max-width: calc(100vw - 30px);
            }

            .btn-enhanced-topbar {
                padding: 8px 16px !important;
                font-size: 13px !important;
            }
        }
    </style>

    <!-- Professional Loading Spinner JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const loadingSpinner = document.getElementById('loading-spinner');
        let isNavigating = false;

        // Show loading spinner for navigation
        function showLoadingSpinner() {
            if (loadingSpinner && !isNavigating) {
                isNavigating = true;
                loadingSpinner.style.display = 'flex';
                loadingSpinner.classList.remove('hidden');

                // Force reflow to ensure display change takes effect
                loadingSpinner.offsetHeight;
            }
        }

        // Hide loading spinner
        function hideLoadingSpinner() {
            if (loadingSpinner) {
                isNavigating = false;
                loadingSpinner.classList.add('hidden');
                setTimeout(() => {
                    if (loadingSpinner && !isNavigating) {
                        loadingSpinner.style.display = 'none';
                    }
                }, 300);
            }
        }

        // Hide spinner when page is ready
        function ensureSpinnerHidden() {
            if (document.readyState === 'complete') {
                hideLoadingSpinner();
            }
        }

        // Multiple event listeners to ensure spinner is hidden
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', ensureSpinnerHidden);
        } else {
            ensureSpinnerHidden();
        }

        window.addEventListener('load', ensureSpinnerHidden);

        // Show spinner for navigation links
        document.addEventListener('click', function(e) {
            const link = e.target.closest('a');
            if (link &&
                link.href &&
                !link.href.startsWith('#') &&
                !link.href.startsWith('javascript:') &&
                !link.href.startsWith('mailto:') &&
                !link.href.startsWith('tel:') &&
                !link.target &&
                !link.hasAttribute('data-bs-toggle') &&
                !link.classList.contains('no-loading') &&
                !link.classList.contains('dropdown-item')) {

                // Only show spinner for internal links
                try {
                    const linkUrl = new URL(link.href);
                    const currentUrl = new URL(window.location.href);
                    if (linkUrl.hostname === currentUrl.hostname) {
                        showLoadingSpinner();
                    }
                } catch (e) {
                    // If URL parsing fails, assume it's internal
                    showLoadingSpinner();
                }
            }
        });

        // Show spinner for form submissions
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form && !form.classList.contains('no-loading')) {
                showLoadingSpinner();
            }
        });

        // Handle browser navigation events
        window.addEventListener('beforeunload', function() {
            showLoadingSpinner();
        });

        window.addEventListener('pageshow', function(e) {
            hideLoadingSpinner();
        });

        window.addEventListener('pagehide', function() {
            hideLoadingSpinner();
        });

        // Fallback: Hide spinner after reasonable time
        setTimeout(() => {
            hideLoadingSpinner();
        }, 3000); // Reduced to 3 seconds
    });
    </script>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    // line 6
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Capitol Academy - Financial Markets Education";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Capitol Academy offers exceptional education programs in financial markets, trading strategies, and professional development for traders worldwide.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 8
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_keywords(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_keywords"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_keywords"));

        yield "financial markets, trading education, forex, technical analysis, fundamental analysis, capitol academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 46
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 47
        yield "        ";
        yield $this->env->getRuntime('Symfony\Bridge\Twig\Extension\ImportMapRuntime')->importmap("app");
        yield "
        <style>
            /* Capitol Academy Typography & Color Scheme */
            :root {
                --ca-red: #971020;
                --ca-gray: #45403f;
                --ca-blue: #00233e;
                --ca-green: #99b75a;
            }

            /* Typography */
            h1, h2, h3, h4, h5, h6, .heading-font {
                font-family: 'Montserrat', sans-serif !important;
            }

            body, p, .body-font {
                font-family: 'Calibri', Arial, sans-serif !important;
            }

            /* Professional Navigation Styles */
            .nav-link-hover {
                transition: all 0.3s ease;
                position: relative;
            }

            .nav-link-hover:hover {
                color: #a90418 !important;
                transform: translateY(-1px);
            }

            /* Remove underline bars */

            /* Button Hover Effects */
            .btn-hover-effect {
                transition: all 0.3s ease;
            }

            .btn-hover-effect:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
            }

            /* Social Media Icons */
            .social-btn {
                transition: all 0.3s ease;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
                color: white !important;
                background: transparent;
            }

            .social-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
                background: rgba(255, 255, 255, 0.1) !important;
                border-color: white !important;
                color: white !important;
            }

            .social-btn i {
                font-size: 1rem;
                color: inherit;
            }

            /* Footer Social Media Icons - Specific styling for footer */
            .footer-social-btn {
                transition: all 0.3s ease;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border: 2px solid #2c3e50 !important;
                color: #2c3e50 !important;
                background: transparent;
            }

            .footer-social-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
                background: #2c3e50 !important;
                border-color: #2c3e50 !important;
                color: white !important;
            }

            .footer-social-btn i {
                font-size: 1rem;
                color: inherit;
            }

            /* Dropdown Menu Styles */
            .dropdown-menu {
                border: none;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                border-radius: 8px;
                padding: 0.5rem 0;
            }

            .dropdown-item {
                transition: all 0.3s ease;
                padding: 0.75rem 1.5rem;
            }

            .dropdown-item:hover {
                background-color: #f8f9fa !important;
                color: #011a2d !important;
                transform: translateX(5px);
            }

            .dropdown-item {
                color: #011a2d !important;
            }

            /* Professional Text Colors */
            .text-on-white { color: #011a2d !important; }
            .text-on-dark { color: white !important; }
            .text-on-red { color: white !important; }

            /* WCAG Accessibility Colors */
            .bg-white, .bg-light { color: #011a2d; }
            .bg-dark, .bg-primary { color: white; }
            .bg-danger, .bg-warning { color: white; }

            /* Footer Link Styles */
            .footer-link {
                transition: all 0.3s ease;
                opacity: 0.8;
                color: #2c3e50 !important;
            }

            .footer-link:hover {
                opacity: 1;
                color: #a90418 !important;
                transform: translateX(3px);
            }

            /* Professional Button Styles */
            .btn-professional {
                background: linear-gradient(135deg, #011a2d 0%, #1a3a5c 100%);
                border: none;
                color: white;
                transition: all 0.3s ease;
            }

            .btn-professional:hover {
                background: linear-gradient(135deg, #1a3a5c 0%, #011a2d 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
                color: white;
            }
        </style>
    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 387
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 899
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 900
        yield "        ";
        yield from $this->unwrap()->yieldBlock('importmap', $context, $blocks);
        // line 901
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 900
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_importmap(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "importmap"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "importmap"));

        yield $this->env->getRuntime('Symfony\Bridge\Twig\Extension\ImportMapRuntime')->importmap("app");
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "base.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  2231 => 900,  2220 => 901,  2217 => 900,  2204 => 899,  2182 => 387,  2012 => 47,  1999 => 46,  1976 => 8,  1953 => 7,  1930 => 6,  993 => 902,  991 => 899,  655 => 566,  543 => 457,  539 => 456,  535 => 455,  531 => 454,  519 => 445,  509 => 438,  505 => 437,  461 => 398,  451 => 391,  446 => 388,  444 => 387,  439 => 384,  433 => 383,  423 => 379,  418 => 378,  413 => 377,  409 => 376,  401 => 370,  385 => 357,  369 => 344,  366 => 343,  355 => 335,  344 => 327,  334 => 320,  330 => 319,  326 => 317,  319 => 314,  316 => 313,  308 => 311,  306 => 310,  296 => 303,  292 => 301,  285 => 298,  282 => 297,  274 => 295,  272 => 294,  265 => 289,  263 => 288,  240 => 268,  230 => 261,  226 => 260,  222 => 259,  218 => 258,  204 => 247,  192 => 238,  188 => 237,  184 => 236,  163 => 220,  157 => 217,  150 => 212,  148 => 211,  144 => 209,  142 => 208,  137 => 205,  135 => 46,  121 => 34,  119 => 33,  110 => 27,  103 => 23,  99 => 22,  93 => 19,  87 => 16,  83 => 15,  77 => 12,  70 => 8,  66 => 7,  62 => 6,  55 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>{% block title %}Capitol Academy - Financial Markets Education{% endblock %}</title>
    <meta name=\"description\" content=\"{% block meta_description %}Capitol Academy offers exceptional education programs in financial markets, trading strategies, and professional development for traders worldwide.{% endblock %}\">
    <meta name=\"keywords\" content=\"{% block meta_keywords %}financial markets, trading education, forex, technical analysis, fundamental analysis, capitol academy{% endblock %}\">

    <!-- Professional Favicon System -->
    <!-- Standard favicon -->
    <link rel=\"icon\" type=\"image/png\" href=\"{{ asset('images/logos/logo-round.png') }}\">

    <!-- PNG favicons for different sizes -->
    <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"{{ asset('images/logos/logo-round.png') }}\">
    <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"{{ asset('images/logos/logo-round.png') }}\">

    <!-- Apple Touch Icon for iOS devices -->
    <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"{{ asset('images/logos/logo-round.png') }}\">

    <!-- Android Chrome icons -->
    <link rel=\"icon\" type=\"image/png\" sizes=\"192x192\" href=\"{{ asset('images/logos/logo-round.png') }}\">
    <link rel=\"icon\" type=\"image/png\" sizes=\"512x512\" href=\"{{ asset('images/logos/logo-round.png') }}\">

    <!-- Microsoft Windows tiles -->
    <meta name=\"msapplication-TileColor\" content=\"#081c2c\">
    <meta name=\"msapplication-config\" content=\"{{ asset('favicons/browserconfig.xml') }}\">

    <!-- Theme colors for mobile browsers -->
    <meta name=\"theme-color\" content=\"#081c2c\">

    <!-- SEO Meta Tags -->
    {% include 'components/seo_meta.html.twig' %}

    <!-- Bootstrap CSS -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <!-- Font Awesome Icons -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">

    <!-- Google Fonts -->
    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">
    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>
    <link href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap\" rel=\"stylesheet\">

    {% block stylesheets %}
        {{ importmap('app') }}
        <style>
            /* Capitol Academy Typography & Color Scheme */
            :root {
                --ca-red: #971020;
                --ca-gray: #45403f;
                --ca-blue: #00233e;
                --ca-green: #99b75a;
            }

            /* Typography */
            h1, h2, h3, h4, h5, h6, .heading-font {
                font-family: 'Montserrat', sans-serif !important;
            }

            body, p, .body-font {
                font-family: 'Calibri', Arial, sans-serif !important;
            }

            /* Professional Navigation Styles */
            .nav-link-hover {
                transition: all 0.3s ease;
                position: relative;
            }

            .nav-link-hover:hover {
                color: #a90418 !important;
                transform: translateY(-1px);
            }

            /* Remove underline bars */

            /* Button Hover Effects */
            .btn-hover-effect {
                transition: all 0.3s ease;
            }

            .btn-hover-effect:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
            }

            /* Social Media Icons */
            .social-btn {
                transition: all 0.3s ease;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
                color: white !important;
                background: transparent;
            }

            .social-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
                background: rgba(255, 255, 255, 0.1) !important;
                border-color: white !important;
                color: white !important;
            }

            .social-btn i {
                font-size: 1rem;
                color: inherit;
            }

            /* Footer Social Media Icons - Specific styling for footer */
            .footer-social-btn {
                transition: all 0.3s ease;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border: 2px solid #2c3e50 !important;
                color: #2c3e50 !important;
                background: transparent;
            }

            .footer-social-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
                background: #2c3e50 !important;
                border-color: #2c3e50 !important;
                color: white !important;
            }

            .footer-social-btn i {
                font-size: 1rem;
                color: inherit;
            }

            /* Dropdown Menu Styles */
            .dropdown-menu {
                border: none;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                border-radius: 8px;
                padding: 0.5rem 0;
            }

            .dropdown-item {
                transition: all 0.3s ease;
                padding: 0.75rem 1.5rem;
            }

            .dropdown-item:hover {
                background-color: #f8f9fa !important;
                color: #011a2d !important;
                transform: translateX(5px);
            }

            .dropdown-item {
                color: #011a2d !important;
            }

            /* Professional Text Colors */
            .text-on-white { color: #011a2d !important; }
            .text-on-dark { color: white !important; }
            .text-on-red { color: white !important; }

            /* WCAG Accessibility Colors */
            .bg-white, .bg-light { color: #011a2d; }
            .bg-dark, .bg-primary { color: white; }
            .bg-danger, .bg-warning { color: white; }

            /* Footer Link Styles */
            .footer-link {
                transition: all 0.3s ease;
                opacity: 0.8;
                color: #2c3e50 !important;
            }

            .footer-link:hover {
                opacity: 1;
                color: #a90418 !important;
                transform: translateX(3px);
            }

            /* Professional Button Styles */
            .btn-professional {
                background: linear-gradient(135deg, #011a2d 0%, #1a3a5c 100%);
                border: none;
                color: white;
                transition: all 0.3s ease;
            }

            .btn-professional:hover {
                background: linear-gradient(135deg, #1a3a5c 0%, #011a2d 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
                color: white;
            }
        </style>
    {% endblock %}
</head>
<body>
    <!-- Loading Spinner -->
    {% include 'components/loading_spinner.html.twig' %}

    <!-- Promotional Banner (Above Navigation) -->
    {% include 'components/promotional_banner.html.twig' %}

    <!-- Navigation -->
    <nav class=\"navbar navbar-expand-lg navbar-light shadow-lg sticky-top\" style=\"background-color: #ffffff; z-index: 1030;\">
        <div class=\"container\">
            <!-- Left-aligned Logo to match courses section -->
            <a class=\"navbar-brand d-flex align-items-center py-2\" href=\"{{ path('app_home') }}\" style=\"margin-left: 0;\">
                <div class=\"logo-container me-3\">
                    <!-- Use Horizontal Logo for topbar -->
                    <img src=\"{{ asset('images/logos/logo-horizontal.png') }}\" alt=\"Capitol Academy\" class=\"logo-img\" style=\"height: 40px; width: auto; max-width: 180px;\" onerror=\"this.src='{{ asset('images/logos/logo-round.png') }}'\">
                </div>
            </a>

            <button class=\"navbar-toggler border-0 p-2\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarNav\" aria-controls=\"navbarNav\" aria-expanded=\"false\" aria-label=\"Toggle navigation\">
                <span class=\"navbar-toggler-icon\"></span>
            </button>

            <div class=\"collapse navbar-collapse\" id=\"navbarNav\">
                <!-- Centered Navigation Menu -->
                <ul class=\"navbar-nav mx-auto\">
                    <li class=\"nav-item dropdown\">
                        <a class=\"nav-link dropdown-toggle px-3 mx-1 nav-link-hover\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 400;\">
                            Our Services
                        </a>
                        <ul class=\"dropdown-menu shadow border-0 rounded-3\">
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_courses_list') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-graduation-cap me-2\"></i>Trading Courses</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_videos') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-video me-2\"></i>Trading Videos</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_contact_webinar') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-broadcast-tower me-2\"></i>Live Trading Webinars</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"#\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chart-line me-2\"></i>Buy/Sell Signals</a></li>
                        </ul>
                    </li>
                    <li class=\"nav-item dropdown\">
                        <a class=\"nav-link dropdown-toggle px-3 mx-1 nav-link-hover\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 400;\">
                            Trading Tools
                        </a>
                        <ul class=\"dropdown-menu shadow border-0 rounded-3\">
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_market_analysis') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chart-bar me-2\"></i>Market Analysis</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"https://www.youtube.com/@capitolacademy1\" target=\"_blank\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fab fa-youtube me-2\"></i>YouTube Channel</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"#\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-calendar-alt me-2\"></i>Economic Calendar</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"#\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chart-line me-2\"></i>Live Charts</a></li>
                        </ul>
                    </li>
                    <li class=\"nav-item dropdown\">
                        <a class=\"nav-link dropdown-toggle px-3 mx-1 nav-link-hover\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 400;\">
                            Company
                        </a>
                        <ul class=\"dropdown-menu shadow border-0 rounded-3\">
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_about') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-info-circle me-2\"></i>About Us</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_instructors') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-chalkboard-teacher me-2\"></i>Instructors</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_partnership') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-handshake me-2\"></i>Partners</a></li>
                            <li><a class=\"dropdown-item py-2\" href=\"{{ path('app_contact') }}\" style=\"font-family: 'Montserrat', sans-serif; font-weight: 400;\"><i class=\"fas fa-envelope me-2\"></i>Contact Us</a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Enhanced Search Box with Autocomplete - Right aligned to match courses section -->
                <div class=\"d-flex align-items-center me-0 position-relative\" style=\"margin-right: 0;\">
                    <form class=\"d-flex me-2\" role=\"search\" action=\"{{ path('app_search') }}\" method=\"GET\">
                        <div class=\"search-container position-relative\">
                            <input class=\"form-control form-control-sm search-input-enhanced\"
                                   type=\"search\"
                                   name=\"q\"
                                   placeholder=\"Search...\"
                                   aria-label=\"Search\"
                                   style=\"width: 200px; border-radius: 6px; border: 2px solid #e0e0e0; padding: 8px 12px;\"
                                   autocomplete=\"off\"
                                   id=\"globalSearchInput\">
                            <div class=\"search-suggestions\" id=\"searchSuggestions\"></div>
                        </div>
                        <button class=\"btn btn-sm ms-1 search-btn-enhanced\" type=\"submit\" style=\"border-radius: 6px; border: 2px solid #011a2d; background: #011a2d; color: white; padding: 8px 12px;\">
                            <i class=\"fas fa-search\"></i>
                        </button>
                    </form>
                </div>

                <!-- Right-aligned buttons to match courses section margin -->
                <div class=\"d-flex gap-2\" style=\"margin-right: 0;\">
                    {% if app.user %}
                        <!-- Professional User Profile Dropdown -->
                        <div class=\"dropdown user-profile-dropdown\">
                            <a class=\"btn btn-profile-dropdown dropdown-toggle d-flex align-items-center\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">
                                <!-- User Avatar -->
                                <div class=\"user-avatar-small me-2\">
                                    {% if app.user.profilePicture %}
                                        <img src=\"{{ asset('uploads/profiles/' ~ app.user.profilePicture) }}\" alt=\"{{ app.user.fullName }}\" class=\"rounded-circle\">
                                    {% else %}
                                        <div class=\"user-initials-avatar\">
                                            {{ app.user.firstName|first|upper }}{{ app.user.lastName|first|upper }}
                                        </div>
                                    {% endif %}
                                </div>
                                <!-- User Name -->
                                <span class=\"user-name\">{{ app.user.fullName }}</span>
                            </a>
                            <ul class=\"dropdown-menu dropdown-menu-end user-dropdown-menu\">
                                <!-- Compact User Info Header -->
                                <li class=\"dropdown-header user-dropdown-header\">
                                    <div class=\"d-flex align-items-center\">
                                        <div class=\"user-avatar-compact me-2\">
                                            {% if app.user.profilePicture %}
                                                <img src=\"{{ asset('uploads/profiles/' ~ app.user.profilePicture) }}\" alt=\"{{ app.user.fullName }}\" class=\"rounded-circle\">
                                            {% else %}
                                                <div class=\"user-initials-compact\">
                                                    {{ app.user.firstName|first|upper }}{{ app.user.lastName|first|upper }}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class=\"user-info-compact\">
                                            <div class=\"user-name-compact\">{{ app.user.fullName }}</div>
                                            <small class=\"user-email-compact\">{{ app.user.email }}</small>
                                        </div>
                                    </div>
                                </li>

                                <!-- Profile Option -->
                                <li>
                                    <a class=\"dropdown-item dropdown-item-capitol\" href=\"{{ path('app_user_profile') }}\">
                                        <i class=\"fas fa-user-circle me-2\"></i>
                                        <span>My Profile</span>
                                    </a>
                                </li>

                                <!-- Sign Out Option -->
                                <li>
                                    <a class=\"dropdown-item dropdown-item-capitol dropdown-item-logout\" href=\"{{ path('app_logout') }}\">
                                        <i class=\"fas fa-sign-out-alt me-2\"></i>
                                        <span>Sign Out</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    {% else %}
                        <!-- Enhanced Guest User Menu -->
                        <a href=\"{{ path('app_register') }}\" class=\"btn btn-primary btn-enhanced-topbar me-2\" style=\"
                            background-color: #011a2d;
                            border: 2px solid #011a2d;
                            color: white;
                            font-weight: 600;
                            border-radius: 6px;
                            padding: 8px 20px;
                            transition: all 0.3s ease;
                            text-decoration: none;
                        \" onmouseover=\"this.style.backgroundColor='#dc3545'; this.style.borderColor='#dc3545'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(220, 53, 69, 0.3)'\"
                           onmouseout=\"this.style.backgroundColor='#011a2d'; this.style.borderColor='#011a2d'; this.style.transform='translateY(0)'; this.style.boxShadow='none'\">
                            <i class=\"fas fa-user-plus me-2\"></i>Join for Free
                        </a>
                        <a href=\"{{ path('app_login') }}\" class=\"btn btn-outline-dark btn-enhanced-topbar\" style=\"
                            border: 2px solid #011a2d;
                            color: #011a2d;
                            border-radius: 6px;
                            padding: 8px 20px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            text-decoration: none;
                        \" onmouseover=\"this.style.backgroundColor='#011a2d'; this.style.color='white'\"
                           onmouseout=\"this.style.backgroundColor='transparent'; this.style.color='#011a2d'\">
                            <i class=\"fas fa-sign-in-alt me-2\"></i>Login
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% for type, messages in app.flashes %}
        {% for message in messages %}
            <div class=\"alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show\" role=\"alert\">
                {{ message }}
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
            </div>
        {% endfor %}
    {% endfor %}

    <!-- Main Content -->
    <main>
        {% block body %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class=\"py-4\" style=\"background: url('{{ asset('images/backgrounds/Background 2 et bas de page HP.png') }}') center/cover; color: #2c3e50; position: relative; margin-top: 6rem; width: 100%;\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- Left Column -->
                <div class=\"col-md-6\">
                    <!-- Capitol Academy Logo -->
                    <div class=\"mb-4\">
                        <img src=\"{{ asset('images/logos/logo-round.png') }}\" alt=\"Capitol Academy\" class=\"mb-3\" style=\"height: 100px; width: 100px; border-radius: 50%;\" onerror=\"this.src='{{ asset('images/placeholders/image-placeholder.png') }}'\">
                    </div>

                    <!-- Social Media Icons -->
                    <div class=\"mb-4\">
                        <div class=\"d-flex flex-wrap gap-2\">
                            <a href=\"https://www.facebook.com/CapitolAcademyTunisie/\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Follow us on Facebook\">
                                <i class=\"fab fa-facebook-f\"></i>
                            </a>
                            <a href=\"https://www.instagram.com/capitol_academy_global/\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Follow us on Instagram\">
                                <i class=\"fab fa-instagram\"></i>
                            </a>
                            <a href=\"https://www.youtube.com/@capitolacademy1\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Subscribe to our YouTube Channel\">
                                <i class=\"fab fa-youtube\"></i>
                            </a>
                            <a href=\"https://tn.linkedin.com/company/capitol-academy-tunisie\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Connect with us on LinkedIn\">
                                <i class=\"fab fa-linkedin-in\"></i>
                            </a>
                            <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"btn btn-outline-dark btn-sm footer-social-btn\" title=\"Contact us on WhatsApp\">
                                <i class=\"fab fa-whatsapp\"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Legal Links -->
                    <div class=\"mb-3\">
                        <a href=\"#\" class=\"text-decoration-none small footer-link me-3 fw-bold\" style=\"color: #2c3e50;\">Terms</a>
                        <a href=\"#\" class=\"text-decoration-none small footer-link me-3 fw-bold\" style=\"color: #2c3e50;\">Privacy Policy</a>
                        <a href=\"#\" class=\"text-decoration-none small footer-link me-3 fw-bold\" style=\"color: #2c3e50;\">Cookie Policy</a>
                        <a href=\"#\" class=\"text-decoration-none small footer-link fw-bold\" style=\"color: #2c3e50;\">Editorial Guidelines</a>
                    </div>
                </div>

                <!-- Right Column Navigation -->
                <div class=\"col-md-6\">
                    <div class=\"row\">
                        <div class=\"col-md-4\">
                            <h6 class=\"fw-bold mb-3\" style=\"color: #2c3e50;\">Our Services</h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\"><a href=\"{{ path('app_videos') }}\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Trading Videos</a></li>
                                <li class=\"mb-2\"><a href=\"{{ path('app_contact_webinar') }}\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Live Trading Webinars</a></li>
                                <li class=\"mb-2\"><a href=\"#\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Buy/Sell Signals</a></li>
                            </ul>
                        </div>
                        <div class=\"col-md-4\">
                            <h6 class=\"fw-bold mb-3\" style=\"color: #2c3e50;\">Trading Tools</h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\"><a href=\"{{ path('app_market_analysis') }}\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Market Analysis</a></li>
                                <li class=\"mb-2\"><a href=\"https://www.youtube.com/@capitolacademy1\" target=\"_blank\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Youtube Channel</a></li>
                                <li class=\"mb-2\"><a href=\"#\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Economic Calendar</a></li>
                                <li class=\"mb-2\"><a href=\"#\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Live Charts</a></li>
                            </ul>
                        </div>
                        <div class=\"col-md-4\">
                            <h6 class=\"fw-bold mb-3\" style=\"color: #2c3e50;\">Company</h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\"><a href=\"{{ path('app_about') }}\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">About us</a></li>
                                <li class=\"mb-2\"><a href=\"{{ path('app_instructors') }}\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Instructors</a></li>
                                <li class=\"mb-2\"><a href=\"{{ path('app_partnership') }}\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Partners</a></li>
                                <li class=\"mb-2\"><a href=\"{{ path('app_contact') }}\" class=\"text-decoration-none footer-link\" style=\"color: #2c3e50;\">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <hr class=\"my-4\" style=\"border-color: rgba(44,62,80,0.3);\">

            <!-- Bottom Section -->
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Full Disclaimer Text -->
                    <div class=\"legal-disclaimer mb-4\">
                        <p class=\"mb-3\" style=\"color: #2c3e50; font-size: 0.8rem; line-height: 1.5;\">
                            <strong>Risk Warning:</strong> Trading financial instruments carries a high level of risk and may not be suitable for all investors. The high degree of leverage can work against you as well as for you. Before deciding to trade, you should carefully consider your investment objectives, level of experience, and risk appetite. There is a possibility that you may sustain a loss of some or all of your initial investment and therefore you should not invest money that you cannot afford to lose. You should be aware of all the risks associated with trading and seek advice from an independent financial advisor if you have any doubts.
                            <br><br>
                            All content on this website is for educational purposes only and should not be considered as financial advice. All trading involves risk and past performance is not indicative of future results. The information provided does not constitute investment advice and we strongly recommend that you seek independent financial advice before making any investment decisions.
                            <br><br>
                            Capitol Academy provides educational content and training materials for informational purposes only. We do not guarantee the accuracy, completeness, or usefulness of any information provided. Any reliance you place on such information is strictly at your own risk. Capitol Academy disclaims all liability and responsibility arising from any reliance placed on such materials by you or any other visitor to the website, or by anyone who may be informed of any of its contents.
                        </p>
                    </div>

                    <!-- reCAPTCHA Notice -->
                    <div class=\"text-center mb-2\">
                        <p class=\"mb-0\" style=\"color: #2c3e50; font-size: 0.75rem;\">
                            This site is protected by reCAPTCHA and the Google
                            <a href=\"https://policies.google.com/privacy\" style=\"color: #2c3e50;\">Privacy Policy</a> and
                            <a href=\"https://policies.google.com/terms\" style=\"color: #2c3e50;\">Terms of Service</a> apply.
                        </p>
                    </div>

                    <!-- Copyright -->
                    <div class=\"text-center\">
                        <p class=\"mb-0\" style=\"color: #2c3e50; font-size: 0.75rem;\">&copy; 2025 Capitol Academy. All rights reserved.</p>
                        <small style=\"color: #2c3e50; opacity: 0.8; font-size: 0.7rem;\">Registration #1264639G</small>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <!-- Global Search Autocomplete -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('globalSearchInput');
        const suggestionsContainer = document.getElementById('searchSuggestions');
        let currentSuggestions = [];
        let selectedIndex = -1;
        let searchTimeout;

        if (!searchInput || !suggestionsContainer) return;

        // Handle input events
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                hideSuggestions();
                return;
            }

            searchTimeout = setTimeout(() => {
                fetchSuggestions(query);
            }, 300);
        });

        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            if (!suggestionsContainer.classList.contains('show')) return;

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, currentSuggestions.length - 1);
                    updateSelection();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    updateSelection();
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && currentSuggestions[selectedIndex]) {
                        window.location.href = currentSuggestions[selectedIndex].url;
                    } else {
                        this.closest('form').submit();
                    }
                    break;
                case 'Escape':
                    hideSuggestions();
                    break;
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                hideSuggestions();
            }
        });

        function fetchSuggestions(query) {
            fetch(`{{ path('api_search_autocomplete') }}?q=\${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.suggestions) {
                        currentSuggestions = data.suggestions;
                        displaySuggestions(data.suggestions);
                    }
                })
                .catch(error => {
                    console.error('Search error:', error);
                });
        }

        function displaySuggestions(suggestions) {
            if (suggestions.length === 0) {
                hideSuggestions();
                return;
            }

            // Sort suggestions to show priority items first
            const sortedSuggestions = suggestions.sort((a, b) => {
                if (a.priority && !b.priority) return -1;
                if (!a.priority && b.priority) return 1;
                return 0;
            });

            suggestionsContainer.innerHTML = sortedSuggestions.map((suggestion, index) => `
                <div class=\"suggestion-item \${suggestion.priority ? 'priority-suggestion' : ''}\" data-index=\"\${index}\" data-url=\"\${suggestion.url}\">
                    <i class=\"\${suggestion.icon} suggestion-icon\"></i>
                    <div class=\"suggestion-content\">
                        <div class=\"suggestion-title\">\${suggestion.title}</div>
                        <div class=\"suggestion-category\">\${suggestion.category}</div>
                    </div>
                </div>
            `).join('');

            // Add click handlers
            suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', function() {
                    window.location.href = this.dataset.url;
                });
            });

            selectedIndex = -1;
            suggestionsContainer.classList.add('show');
        }

        function updateSelection() {
            const items = suggestionsContainer.querySelectorAll('.suggestion-item');
            items.forEach((item, index) => {
                item.classList.toggle('active', index === selectedIndex);
            });
        }

        function hideSuggestions() {
            suggestionsContainer.classList.remove('show');
            selectedIndex = -1;
        }
    });
    </script>

    <!-- Enhanced Dropdown Functionality with Auto-Close -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all Bootstrap dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // Enhanced dropdown behavior for navbar with auto-close on mouse leave
            const navDropdowns = document.querySelectorAll('.navbar-nav .dropdown');

            navDropdowns.forEach(dropdown => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                let closeTimeout;

                // Function to close dropdown
                function closeDropdown() {
                    menu.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }

                // Function to open dropdown
                function openDropdown() {
                    // Clear any pending close timeout
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Close other dropdowns
                    navDropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            const otherMenu = otherDropdown.querySelector('.dropdown-menu');
                            const otherToggle = otherDropdown.querySelector('.dropdown-toggle');
                            otherMenu.classList.remove('show');
                            otherToggle.setAttribute('aria-expanded', 'false');
                        }
                    });

                    // Open current dropdown
                    menu.classList.add('show');
                    toggle.setAttribute('aria-expanded', 'true');
                }

                // Add click event for better mobile support
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();

                    const isOpen = menu.classList.contains('show');
                    if (isOpen) {
                        closeDropdown();
                    } else {
                        openDropdown();
                    }
                });

                // Mouse enter event - open dropdown
                dropdown.addEventListener('mouseenter', function(e) {
                    // Clear any pending close timeout
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Only auto-open on desktop (screen width > 768px)
                    if (window.innerWidth > 768) {
                        openDropdown();
                    }
                });

                // Mouse leave event - close dropdown with delay
                dropdown.addEventListener('mouseleave', function(e) {
                    // Only auto-close on desktop and if dropdown is open
                    if (window.innerWidth > 768 && menu.classList.contains('show')) {
                        // Set timeout to close dropdown after 250ms delay
                        closeTimeout = setTimeout(() => {
                            closeDropdown();
                            closeTimeout = null;
                        }, 250);
                    }
                });

                // Mouse enter on dropdown menu - cancel close timeout
                menu.addEventListener('mouseenter', function(e) {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdown.contains(e.target)) {
                        closeDropdown();
                    }
                });

                // Close dropdown when clicking on menu items
                menu.addEventListener('click', function(e) {
                    if (e.target.classList.contains('dropdown-item')) {
                        closeDropdown();
                    }
                });

                // Handle window resize to ensure proper behavior
                window.addEventListener('resize', function() {
                    // Clear timeout on resize
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Close all dropdowns on mobile
                    if (window.innerWidth <= 768) {
                        closeDropdown();
                    }
                });
            });

            // Enhanced user profile dropdown behavior
            const userDropdowns = document.querySelectorAll('.user-profile-dropdown');

            userDropdowns.forEach(dropdown => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                let closeTimeout;

                // Function to close dropdown
                function closeDropdown() {
                    menu.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }

                // Function to open dropdown
                function openDropdown() {
                    // Clear any pending close timeout
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    // Close other dropdowns
                    userDropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            const otherMenu = otherDropdown.querySelector('.dropdown-menu');
                            const otherToggle = otherDropdown.querySelector('.dropdown-toggle');
                            otherMenu.classList.remove('show');
                            otherToggle.setAttribute('aria-expanded', 'false');
                        }
                    });

                    // Open current dropdown
                    menu.classList.add('show');
                    toggle.setAttribute('aria-expanded', 'true');

                    // Fix dropdown positioning to ensure it stays within viewport
                    setTimeout(() => {
                        const rect = menu.getBoundingClientRect();
                        const viewportWidth = window.innerWidth;

                        // Reset any previous transforms
                        menu.style.transform = '';

                        // If dropdown extends beyond right edge of viewport
                        if (rect.right > viewportWidth - 10) {
                            const overflow = rect.right - viewportWidth + 20; // 20px margin
                            menu.style.transform = `translateX(-\${overflow}px)`;
                        }

                        // If dropdown extends beyond left edge of viewport
                        if (rect.left < 10) {
                            menu.style.transform = `translateX(\${Math.abs(rect.left) + 20}px)`;
                        }
                    }, 10);
                }

                // Click event for dropdown toggle
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const isOpen = menu.classList.contains('show');
                    if (isOpen) {
                        closeDropdown();
                    } else {
                        openDropdown();
                    }
                });

                // Mouse enter event - open dropdown (desktop only)
                dropdown.addEventListener('mouseenter', function(e) {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    if (window.innerWidth > 768) {
                        openDropdown();
                    }
                });

                // Mouse leave event - close dropdown with 250ms delay
                dropdown.addEventListener('mouseleave', function(e) {
                    if (window.innerWidth > 768 && menu.classList.contains('show')) {
                        closeTimeout = setTimeout(() => {
                            closeDropdown();
                            closeTimeout = null;
                        }, 250);
                    }
                });

                // Mouse enter on dropdown menu - cancel close timeout
                menu.addEventListener('mouseenter', function(e) {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdown.contains(e.target)) {
                        closeDropdown();
                    }
                });

                // Close dropdown when clicking on menu items
                menu.addEventListener('click', function(e) {
                    if (e.target.classList.contains('dropdown-item') || e.target.closest('.dropdown-item')) {
                        closeDropdown();
                    }
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (closeTimeout) {
                        clearTimeout(closeTimeout);
                        closeTimeout = null;
                    }

                    if (window.innerWidth <= 768) {
                        closeDropdown();
                    }
                });
            });
        });
    </script>

    <!-- Enhanced User Experience Scripts -->
    <script>
        // Handle offline/online status
        window.addEventListener('online', function() {
            console.log('Capitol Academy: Back online');
            // Remove offline indicator if exists
            const offlineIndicator = document.getElementById('offline-indicator');
            if (offlineIndicator) {
                offlineIndicator.remove();
            }
        });

        window.addEventListener('offline', function() {
            console.log('Capitol Academy: Gone offline');
            // Show offline indicator
            const offlineIndicator = document.createElement('div');
            offlineIndicator.id = 'offline-indicator';
            offlineIndicator.innerHTML = '<i class=\"fas fa-wifi me-2\"></i>You are currently offline';
            offlineIndicator.className = 'alert alert-warning position-fixed';
            offlineIndicator.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; margin: 0;';
            document.body.appendChild(offlineIndicator);
        });
    </script>

    {% block javascripts %}
        {% block importmap %}{{ importmap('app') }}{% endblock %}
    {% endblock %}

    <!-- Enhanced Navigation and UI Styles -->
    <style>
        /* Capitol Academy Professional Navigation */
        .navbar {
            background-color: #ffffff !important;
            border-bottom: 2px solid rgba(8, 28, 44, 0.1);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 15px rgba(8, 28, 44, 0.1);
            position: sticky !important;
            top: 0 !important;
            z-index: 1050 !important;
            width: 100% !important;
        }

        .capitol-navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Logo and Brand Styling */
        .logo-container {
            /* Clean logo container with no visual effects */
        }

        .logo-img {
            height: 50px;
            width: auto;
            max-width: 200px;
            display: block;
        }

        /* Mobile logo adjustments */
        @media (max-width: 767.98px) {
            .logo-img {
                height: 40px;
                max-width: 40px;
            }
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: 800;
            color: #fff;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
            letter-spacing: 0.8px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-tagline {
            font-size: 0.8rem;
            color: #dc3545;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Navigation Enhancements */
        .navbar {
            padding: 0.8rem 0;
            min-height: 80px;
            position: relative;
        }

        .nav-link-hover {
            transition: all 0.3s ease;
            position: relative;
            font-weight: 500;
            font-size: 1rem;
            padding: 0.75rem 1rem !important;
            border-radius: 6px;
            margin: 0 0.25rem;
            color: #011a2d !important;
        }

        .nav-link-hover:hover {
            background-color: rgba(30, 60, 114, 0.08) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15);
            color: #1e3c72 !important;
        }

        .nav-link-hover:focus {
            background-color: rgba(30, 60, 114, 0.08) !important;
            color: #1e3c72 !important;
            box-shadow: 0 0 0 2px rgba(30, 60, 114, 0.25);
            outline: none;
        }

        .nav-link-hover:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(30, 60, 114, 0.2);
        }

        .dropdown-menu {
            animation: fadeInDown 0.3s ease;
            border: 1px solid rgba(30, 60, 114, 0.1);
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            margin-top: 0.25rem;
            background: #ffffff;
            z-index: 1060 !important;
            min-width: 200px;
        }

        .dropdown-menu.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .dropdown-item {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 10px;
            margin: 0 1rem;
            padding: 12px 20px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .dropdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .dropdown-item:hover::before {
            left: 100%;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            transform: translateX(8px) scale(1.02);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
        }

        .dropdown-item:active {
            background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
            color: white;
            transform: translateX(8px) scale(0.98);
        }

        .dropdown-toggle::after {
            transition: transform 0.3s ease;
        }

        .dropdown-toggle[aria-expanded=\"true\"]::after {
            transform: rotate(180deg);
        }

        .nav-item.dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        @media (min-width: 992px) {
            .navbar-nav .dropdown:hover .dropdown-menu {
                display: block;
                animation: fadeInDown 0.3s ease;
            }
        }

        .btn-hover-effect {
            transition: all 0.3s ease;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Social Media Button Enhancements */
        .social-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .social-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .social-btn.btn-outline-light:hover {
            background-color: #fff;
            color: #333;
        }

        /* Enhanced Search Box Styling */
        .search-input-enhanced {
            border: 2px solid #011a2d !important;
            border-radius: 25px !important;
            padding: 8px 16px !important;
            color: #011a2d !important;
            background-color: white !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
        }

        .search-input-enhanced:focus {
            border-color: #011a2d !important;
            box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
            outline: none !important;
            transform: translateY(-1px) !important;
        }

        .search-input-enhanced::placeholder {
            color: rgba(1, 26, 45, 0.6) !important;
        }

        .search-btn-enhanced {
            background-color: #011a2d !important;
            border: 2px solid #011a2d !important;
            color: white !important;
            border-radius: 25px !important;
            padding: 8px 12px !important;
            transition: all 0.3s ease !important;
            font-weight: 600 !important;
        }

        .search-btn-enhanced:hover {
            background-color: #a90418 !important;
            border-color: #a90418 !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3) !important;
        }

        /* Search Suggestions Dropdown */
        .search-container {
            position: relative;
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.15);
            z-index: 1050;
            max-height: 400px;
            overflow-y: auto;
            display: none;
        }

        .search-suggestions.show {
            display: block;
        }

        .suggestion-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .suggestion-item.active {
            background-color: #011a2d;
            color: white;
        }

        .suggestion-icon {
            color: #011a2d;
            width: 16px;
            text-align: center;
        }

        .suggestion-item.active .suggestion-icon {
            color: white;
        }

        .suggestion-content {
            flex: 1;
        }

        .suggestion-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .suggestion-category {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .suggestion-item.active .suggestion-category {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Priority suggestions styling */
        .priority-suggestion {
            background-color: #f0f8ff;
            border-left: 4px solid #011a2d;
            font-weight: 600;
        }

        .priority-suggestion:hover {
            background-color: #e6f3ff;
        }

        .priority-suggestion.active {
            background-color: #011a2d;
            color: white;
        }

        .priority-suggestion .suggestion-icon {
            color: #dc3545;
        }

        .priority-suggestion.active .suggestion-icon {
            color: white;
        }

        /* Capitol Academy Enhanced Topbar Buttons */
        .btn-enhanced-topbar {
            border-radius: 6px !important;
            padding: 10px 20px !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            border-width: 2px !important;
            font-size: 14px !important;
            letter-spacing: 0.5px !important;
        }

        .btn-enhanced-topbar:hover {
            transform: translateY(-2px) !important;
        }

        /* Join for Free Button - Capitol Academy Dark Blue with Red Hover */
        .btn-enhanced-topbar.btn-success {
            background-color: #011a2d !important;
            border-color: #011a2d !important;
            color: white !important;
        }

        .btn-enhanced-topbar.btn-success:hover {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4) !important;
        }

        /* Login Button - Outline Dark Blue */
        .btn-enhanced-topbar.btn-outline-dark {
            color: #011a2d !important;
            border-color: #011a2d !important;
            background-color: transparent !important;
        }

        .btn-enhanced-topbar.btn-outline-dark:hover {
            background-color: #011a2d !important;
            border-color: #011a2d !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.3) !important;
        }

        .social-btn.btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .social-btn.btn-outline-info:hover {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: #fff;
        }

        /* Footer Link Enhancements */
        .footer-link {
            transition: all 0.2s ease;
            text-decoration: none;
            color: #2c3e50 !important;
        }

        .footer-link:hover {
            color: #a90418 !important;
            text-decoration: underline;
        }

        /* Social media icons hover effects - Ensure footer icons are always circular */
        .footer-social-btn {
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            border: 2px solid #2c3e50 !important;
            color: #2c3e50 !important;
            background: transparent !important;
        }

        .footer-social-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
            background: #2c3e50 !important;
            border-color: #2c3e50 !important;
            color: white !important;
        }

        .footer-social-btn i {
            font-size: 1rem !important;
            color: inherit !important;
        }

        /* General social media icons for other contexts */
        .social-btn {
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .social-btn:hover {
            background-color: #2c3e50 !important;
            border-color: #2c3e50 !important;
            color: white !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
        }

        /* Form Button Enhancements */
        .btn-enhanced {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-weight: 600;
            letter-spacing: 0.5px;
            border-width: 2px;
        }

        .btn-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .btn-enhanced:hover::before {
            left: 100%;
        }

        .btn-enhanced:active {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Form Container Enhancements */
        .form-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Animation Keyframes */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Capitol Academy Professional Profile Button */
        .user-profile-dropdown .btn-profile-dropdown {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border: 2px solid rgba(30, 60, 114, 0.2);
            color: white;
            padding: 8px 14px;
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(30, 60, 114, 0.25);
            position: relative;
            overflow: hidden;
        }

        .user-profile-dropdown .btn-profile-dropdown::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .user-profile-dropdown .btn-profile-dropdown:hover::before {
            left: 100%;
        }

        .user-profile-dropdown .btn-profile-dropdown:hover {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-color: rgba(220, 53, 69, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
            color: white;
        }

        .user-profile-dropdown .btn-profile-dropdown:focus {
            box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.25);
            color: white;
            outline: none;
        }

        .user-profile-dropdown .btn-profile-dropdown:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
        }

        /* User Avatar Styles */
        .user-avatar-small {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-avatar-small img {
            width: 30px;
            height: 30px;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(30, 60, 114, 0.2);
        }

        .user-avatar-dropdown {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-avatar-dropdown img {
            width: 48px;
            height: 48px;
            object-fit: cover;
            border: 2px solid #e9ecef;
        }

        .user-initials-avatar {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 11px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 6px rgba(220, 53, 69, 0.2);
        }

        .user-initials-avatar-large {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            border: 2px solid #e9ecef;
        }

        .user-name {
            font-size: 13px;
            font-weight: 600;
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Capitol Academy Professional User Dropdown Menu */
        .user-dropdown-menu {
            min-width: 240px;
            border: 2px solid rgba(30, 60, 114, 0.1);
            border-radius: 8px;
            padding: 0;
            margin-top: 6px;
            background: white;
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.15);
            animation: fadeInDown 0.3s ease;
            /* Fix positioning issues */
            position: absolute !important;
            right: 0 !important;
            left: auto !important;
            transform: none !important;
            /* Ensure dropdown stays within viewport */
            max-width: calc(100vw - 20px);
            overflow: hidden;
        }

        /* Compact User Header */
        .user-dropdown-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 6px 6px 0 0;
            padding: 12px 16px;
            border-bottom: 2px solid rgba(220, 53, 69, 0.2);
            margin: 0;
        }

        .user-avatar-compact {
            width: 32px;
            height: 32px;
            flex-shrink: 0;
        }

        .user-avatar-compact img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 2px solid white;
        }

        .user-initials-compact {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            border: 2px solid white;
        }

        .user-info-compact {
            min-width: 0;
            flex: 1;
        }

        .user-name-compact {
            font-weight: 600;
            font-size: 14px;
            color: white;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-email-compact {
            color: rgba(255, 255, 255, 0.8);
            font-size: 11px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Capitol Academy Dropdown Items */
        .dropdown-item-capitol {
            padding: 10px 16px;
            transition: all 0.3s ease;
            border-radius: 0;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: #011a2d;
            border: none;
            background: transparent;
        }

        .dropdown-item-capitol:hover {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            transform: translateX(3px);
            padding-left: 20px;
        }

        .dropdown-item-capitol:focus {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.3);
            outline: none;
        }

        .dropdown-item-logout:hover {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
        }

        .dropdown-item-logout:focus {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
            box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .dropdown-item-capitol i {
            width: 16px;
            text-align: center;
            font-size: 13px;
        }



        /* Dropdown positioning fix for edge cases */
        .user-profile-dropdown {
            position: relative;
        }

        .user-profile-dropdown .dropdown-menu {
            /* Ensure proper positioning */
            inset: auto 0px auto auto !important;
            transform: translate3d(0px, 42px, 0px) !important;
        }

        /* Handle viewport edge cases */
        @media (max-width: 320px) {
            .user-dropdown-menu {
                min-width: 250px;
                right: -10px !important;
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .navbar-brand span {
                display: none !important;
            }

            .social-btn {
                width: 35px;
                height: 35px;
            }

            .user-name {
                display: none;
            }

            .user-dropdown-menu {
                min-width: 260px;
                /* Better mobile positioning */
                right: 10px !important;
                max-width: calc(100vw - 30px);
            }

            .btn-enhanced-topbar {
                padding: 8px 16px !important;
                font-size: 13px !important;
            }
        }
    </style>

    <!-- Professional Loading Spinner JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const loadingSpinner = document.getElementById('loading-spinner');
        let isNavigating = false;

        // Show loading spinner for navigation
        function showLoadingSpinner() {
            if (loadingSpinner && !isNavigating) {
                isNavigating = true;
                loadingSpinner.style.display = 'flex';
                loadingSpinner.classList.remove('hidden');

                // Force reflow to ensure display change takes effect
                loadingSpinner.offsetHeight;
            }
        }

        // Hide loading spinner
        function hideLoadingSpinner() {
            if (loadingSpinner) {
                isNavigating = false;
                loadingSpinner.classList.add('hidden');
                setTimeout(() => {
                    if (loadingSpinner && !isNavigating) {
                        loadingSpinner.style.display = 'none';
                    }
                }, 300);
            }
        }

        // Hide spinner when page is ready
        function ensureSpinnerHidden() {
            if (document.readyState === 'complete') {
                hideLoadingSpinner();
            }
        }

        // Multiple event listeners to ensure spinner is hidden
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', ensureSpinnerHidden);
        } else {
            ensureSpinnerHidden();
        }

        window.addEventListener('load', ensureSpinnerHidden);

        // Show spinner for navigation links
        document.addEventListener('click', function(e) {
            const link = e.target.closest('a');
            if (link &&
                link.href &&
                !link.href.startsWith('#') &&
                !link.href.startsWith('javascript:') &&
                !link.href.startsWith('mailto:') &&
                !link.href.startsWith('tel:') &&
                !link.target &&
                !link.hasAttribute('data-bs-toggle') &&
                !link.classList.contains('no-loading') &&
                !link.classList.contains('dropdown-item')) {

                // Only show spinner for internal links
                try {
                    const linkUrl = new URL(link.href);
                    const currentUrl = new URL(window.location.href);
                    if (linkUrl.hostname === currentUrl.hostname) {
                        showLoadingSpinner();
                    }
                } catch (e) {
                    // If URL parsing fails, assume it's internal
                    showLoadingSpinner();
                }
            }
        });

        // Show spinner for form submissions
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form && !form.classList.contains('no-loading')) {
                showLoadingSpinner();
            }
        });

        // Handle browser navigation events
        window.addEventListener('beforeunload', function() {
            showLoadingSpinner();
        });

        window.addEventListener('pageshow', function(e) {
            hideLoadingSpinner();
        });

        window.addEventListener('pagehide', function() {
            hideLoadingSpinner();
        });

        // Fallback: Hide spinner after reasonable time
        setTimeout(() => {
            hideLoadingSpinner();
        }, 3000); // Reduced to 3 seconds
    });
    </script>
</body>
</html>
", "base.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\base.html.twig");
    }
}
