<?php

namespace ContainerCxjHiNK;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_Jezs8TRService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.jezs8TR' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.jezs8TR'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'accessControlService' => ['privates', 'App\\Service\\AccessControlService', 'getAccessControlServiceService', true],
            'entityManager' => ['services', 'doctrine.orm.default_entity_manager', 'getDoctrine_Orm_DefaultEntityManagerService', false],
            'videoRepository' => ['privates', 'App\\Repository\\VideoRepository', 'getVideoRepositoryService', true],
        ], [
            'accessControlService' => 'App\\Service\\AccessControlService',
            'entityManager' => '?',
            'videoRepository' => 'App\\Repository\\VideoRepository',
        ]);
    }
}
