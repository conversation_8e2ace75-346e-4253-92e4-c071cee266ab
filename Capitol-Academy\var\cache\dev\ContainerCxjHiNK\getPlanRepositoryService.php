<?php

namespace ContainerCxjHiNK;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getPlanRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\PlanRepository' shared autowired service.
     *
     * @return \App\Repository\PlanRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'PlanRepository.php';

        return $container->privates['App\\Repository\\PlanRepository'] = new \App\Repository\PlanRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
