<?php

namespace ContainerFWomIF9;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_4qavBNKService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.4qavBNK' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.4qavBNK'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'instructor' => ['privates', '.errored..service_locator.4qavBNK.App\\Entity\\Instructor', NULL, 'Cannot autowire service ".service_locator.4qavBNK": it needs an instance of "App\\Entity\\Instructor" but this type has been excluded in "config/services.yaml".'],
        ], [
            'instructor' => 'App\\Entity\\Instructor',
        ]);
    }
}
