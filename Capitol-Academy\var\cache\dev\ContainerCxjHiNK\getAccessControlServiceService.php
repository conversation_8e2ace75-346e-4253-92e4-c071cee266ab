<?php

namespace ContainerCxjHiNK;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAccessControlServiceService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Service\AccessControlService' shared autowired service.
     *
     * @return \App\Service\AccessControlService
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'AccessControlService.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'VdoCipherService.php';

        $a = ($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container));

        if (isset($container->privates['App\\Service\\AccessControlService'])) {
            return $container->privates['App\\Service\\AccessControlService'];
        }
        $b = ($container->privates['monolog.logger'] ?? self::getMonolog_LoggerService($container));

        return $container->privates['App\\Service\\AccessControlService'] = new \App\Service\AccessControlService($a, ($container->privates['App\\Repository\\UserVideoAccessRepository'] ?? $container->load('getUserVideoAccessRepositoryService')), ($container->privates['App\\Repository\\OrderRepository'] ?? $container->load('getOrderRepositoryService')), new \App\Service\VdoCipherService(($container->privates['.debug.http_client'] ?? self::get_Debug_HttpClientService($container)), $b, ($container->privates['parameter_bag'] ??= new \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag($container))), $b);
    }
}
